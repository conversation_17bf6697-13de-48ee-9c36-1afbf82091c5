import ast

def filter_ips_with_realmonitor(file_path):
    filtered_ips = []
    with open(file_path, 'r') as file:
        for i, line in enumerate(file, start=1):
            try:
                # Use ast.literal_eval for Python-style data
                device = ast.literal_eval(line.strip())
                for cam in device.get("cam_cfg", []):
                    for rtsp_link in cam.get("rtsp_list", []):
                        if "/Streaming/Channels/" in rtsp_link:
                            filtered_ips.append(device["ip"])
                            break
            except (ValueError, SyntaxError) as e:
                print(f"Line {i} is invalid: {line.strip()}")
                print(f"Error: {e}")
                continue
    return filtered_ips

file_path = "result2.txt"
ips = filter_ips_with_realmonitor(file_path)

print("Filtered IPs:")
for ip in ips:
    print(ip)

# write to file
with open("filtered_ips_hik.txt", "w") as file:
    for ip in ips:
        file.write(f"{ip}\n")
