import json

# Read JSON data from file.json
with open("tinh_thanh.json", "r", encoding="utf-8") as f:
    data = json.load(f)

# Specify the key containing the list of camera details
key = "Camera"

# Extract camera data
if key in data:
    camera_list = data[key]
    
    # Write IP addresses to file.txt
    with open("tinh_thanh.txt", "w") as f:
        for camera in camera_list:
            f.write(f"{camera['camera_ip']}\n")
else:
    print(f"Key '{key}' not found in the JSON data.")
