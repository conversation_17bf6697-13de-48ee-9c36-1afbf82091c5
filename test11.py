import ast

def calculate_camera_numbers(file_path):
    total_cameras = 0
    
    with open(file_path, 'r') as file:
        for line in file:
            try:
                # Parse the line as a Python dictionary-like structure using ast.literal_eval
                device = ast.literal_eval(line.strip())  # Safely parse the line
                
                # Get the 'number_cameras' value and add it to the total
                total_cameras += device.get('number_cameras', 0)
            except ValueError as e:
                print(f"Error parsing line: {e}")
    
    return total_cameras

# Example usage
file_path = 'ASI_result.txt'  # Path to your file
total_cameras = calculate_camera_numbers(file_path)
print(f"Total number of cameras: {total_cameras}")
