#!/usr/bin/env python3
"""
Script đơn giản để lọc IP từ HTML content hoặc text file
Sử dụng khi đã có HTML content từ trang web
"""

import re
import json
import csv
import time
from typing import List, Dict
from pathlib import Path


class SimpleIPFilter:
    def __init__(self):
        self.ip_pattern = re.compile(r'\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b')
        
    def is_valid_ip(self, ip: str) -> bool:
        """Kiểm tra IP có hợp lệ không"""
        try:
            parts = ip.split('.')
            if len(parts) != 4:
                return False
            
            for part in parts:
                num = int(part)
                if num < 0 or num > 255:
                    return False
            
            # Loại bỏ IP không hợp lệ
            if ip.startswith('0.') or ip.startswith('127.') or ip == '***************':
                return False
            if ip.startswith('192.168.') or ip.startswith('10.') or ip.startswith('172.'):
                return False  # Loại bỏ private IP
                
            return True
        except:
            return False
    
    def extract_ips_from_text(self, text: str) -> List[str]:
        """Trích xuất IP từ text"""
        ips = self.ip_pattern.findall(text)
        valid_ips = []
        
        for ip in ips:
            if self.is_valid_ip(ip):
                valid_ips.append(ip)
        
        # Loại bỏ duplicate
        return list(set(valid_ips))
    
    def extract_ips_from_file(self, file_path: str) -> List[str]:
        """Trích xuất IP từ file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            return self.extract_ips_from_text(content)
        except Exception as e:
            print(f"Lỗi đọc file {file_path}: {e}")
            return []
    
    def save_ips(self, ips: List[str], output_format: str = 'txt') -> str:
        """Lưu danh sách IP ra file"""
        timestamp = int(time.time())
        
        if output_format == 'txt':
            filename = f"filtered_ips_{timestamp}.txt"
            with open(filename, 'w', encoding='utf-8') as f:
                for ip in sorted(ips):
                    f.write(f"{ip}\n")
        
        elif output_format == 'json':
            filename = f"filtered_ips_{timestamp}.json"
            ip_data = [{'ip': ip, 'timestamp': timestamp} for ip in sorted(ips)]
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(ip_data, f, indent=2)
        
        elif output_format == 'csv':
            filename = f"filtered_ips_{timestamp}.csv"
            with open(filename, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerow(['IP', 'Timestamp'])
                for ip in sorted(ips):
                    writer.writerow([ip, timestamp])
        
        print(f"Đã lưu {len(ips)} IP vào file: {filename}")
        return filename
    
    def check_ip_status(self, ips: List[str]) -> Dict[str, bool]:
        """Kiểm tra trạng thái IP (ping test đơn giản)"""
        import subprocess
        import platform
        
        results = {}
        ping_cmd = "ping -n 1" if platform.system().lower() == "windows" else "ping -c 1"
        
        print(f"Đang kiểm tra trạng thái {len(ips)} IP...")
        for i, ip in enumerate(ips, 1):
            try:
                result = subprocess.run(
                    f"{ping_cmd} {ip}".split(),
                    capture_output=True,
                    timeout=3
                )
                results[ip] = result.returncode == 0
                print(f"[{i}/{len(ips)}] {ip}: {'OK' if results[ip] else 'FAIL'}")
            except:
                results[ip] = False
                print(f"[{i}/{len(ips)}] {ip}: TIMEOUT")
        
        return results


def main():
    """Hàm main"""
    print("=== Simple IP Filter ===")
    print("1. Nhập text trực tiếp")
    print("2. Đọc từ file")
    print("3. Kiểm tra IP từ file có sẵn")
    
    choice = input("Chọn option (1-3): ").strip()
    
    filter_tool = SimpleIPFilter()
    ips = []
    
    if choice == '1':
        print("Nhập text chứa IP (nhấn Ctrl+D hoặc Ctrl+Z để kết thúc):")
        try:
            text = ""
            while True:
                line = input()
                text += line + "\n"
        except EOFError:
            pass
        
        ips = filter_tool.extract_ips_from_text(text)
    
    elif choice == '2':
        file_path = input("Nhập đường dẫn file: ").strip()
        if Path(file_path).exists():
            ips = filter_tool.extract_ips_from_file(file_path)
        else:
            print("File không tồn tại!")
            return
    
    elif choice == '3':
        file_path = input("Nhập đường dẫn file chứa danh sách IP: ").strip()
        if Path(file_path).exists():
            with open(file_path, 'r') as f:
                content = f.read()
            ips = [line.strip() for line in content.split('\n') if line.strip()]
            # Lọc chỉ những dòng là IP
            ips = [ip for ip in ips if filter_tool.is_valid_ip(ip)]
        else:
            print("File không tồn tại!")
            return
    
    if not ips:
        print("Không tìm thấy IP nào!")
        return
    
    print(f"Tìm thấy {len(ips)} IP hợp lệ")
    
    # Hiển thị một số IP đầu tiên
    print("Một số IP tìm thấy:")
    for ip in sorted(ips)[:10]:
        print(f"  {ip}")
    if len(ips) > 10:
        print(f"  ... và {len(ips) - 10} IP khác")
    
    # Lưu file
    print("\nChọn format output:")
    print("1. TXT")
    print("2. JSON") 
    print("3. CSV")
    
    format_choice = input("Chọn format (1-3): ").strip()
    format_map = {'1': 'txt', '2': 'json', '3': 'csv'}
    output_format = format_map.get(format_choice, 'txt')
    
    filename = filter_tool.save_ips(ips, output_format)
    
    # Tùy chọn kiểm tra trạng thái IP
    check_status = input("Kiểm tra trạng thái IP? (y/n): ").strip().lower()
    if check_status == 'y':
        status_results = filter_tool.check_ip_status(ips)
        alive_ips = [ip for ip, status in status_results.items() if status]
        
        if alive_ips:
            alive_filename = filter_tool.save_ips(alive_ips, output_format)
            print(f"Đã lưu {len(alive_ips)} IP còn hoạt động vào: {alive_filename}")


if __name__ == "__main__":
    main()
