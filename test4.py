import av

def _is_video_stream(stream):
    return (
            stream.profile is not None
            # and stream.start_time is not None
            and stream.codec_context.format is not None
    )
try:
    print('stream 1')
    with av.open(
            "rtsp://************:554/2",
            timeout=30.0,
            options={"rtsp_transport": "tcp"},
    ) as container:
        print('stream 2')
        stream = container.streams.video[0]
        if _is_video_stream(stream):
            print('stream A')
            # file_name = escape_chars(f"{rtsp_url.lstrip('rtsp://')}.jpg")
            # file_path = PICS_FOLDER / file_name
            # stream.thread_type = "AUTO"
            # for frame in container.decode(video=0):
            #     frame.to_image().save(file_path)
            #     break
            # # console.print(
            # #     f"[bold]Captured screenshot for",
            # #     f"[underline cyan]{rtsp_url}",
            # # )
            # if logger_is_enabled:
            #     logger.debug(f"Captured screenshot for {rtsp_url}")
            # return file_path
        print('stream B')
except Exception as e:
    print('stream C')
