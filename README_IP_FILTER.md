# Script Lọc IP từ TraceMYIP - Quảng Ninh

Bộ script để lọc IP từ trang web https://tools.tracemyip.org/search--state/tinh+quang+ninh

## Các file script

### 1. `ip_filter_quang_ninh.py` - Script chính với Selenium
- Sử dụng Selenium WebDriver để xử lý CAPTCHA
- Tự động trích xuất IP từ trang web
- Hỗ trợ nhiều format output (JSON, CSV, TXT)

### 2. `simple_ip_filter.py` - Script đơn giản
- Lọc IP từ text hoặc file có sẵn
- Không cần Selenium
- Có thể kiểm tra trạng thái IP (ping test)

## Cài đặt

### Cài đặt dependencies cho script chính:
```bash
pip install -r requirements_ip_filter.txt
```

### Cài đặt Chrome WebDriver:
```bash
# Tự động cài đặt
pip install webdriver-manager

# Hoặc tải thủ công từ: https://chromedriver.chromium.org/
```

## Sử dụng

### Script chính (với Selenium):
```bash
python ip_filter_quang_ninh.py
```

Chương trình sẽ hỏi:
1. Format output (JSON/CSV/TXT)
2. Chạy ẩn browser hay không

**Lưu ý**: Khi gặp CAPTCHA, script sẽ dừng và yêu cầu bạn giải thủ công.

### Script đơn giản:
```bash
python simple_ip_filter.py
```

Có 3 options:
1. Nhập text trực tiếp
2. Đọc từ file HTML/text
3. Kiểm tra IP từ file danh sách có sẵn

## Cách thức hoạt động

### Script chính:
1. Mở browser Chrome
2. Truy cập trang web TraceMYIP
3. Phát hiện và chờ giải CAPTCHA
4. Trích xuất IP từ HTML content
5. Lọc IP hợp lệ
6. Lưu kết quả ra file

### Script đơn giản:
1. Đọc content từ input/file
2. Sử dụng regex tìm IP
3. Lọc IP hợp lệ (loại bỏ private IP, invalid IP)
4. Tùy chọn ping test
5. Lưu kết quả

## Format Output

### JSON:
```json
[
  {
    "ip": "*******",
    "context": "Thông tin context xung quanh IP",
    "source": "table"
  }
]
```

### CSV:
```csv
ip,context,source
*******,"Context info",table
```

### TXT:
```
*******
*******
```

## Lọc IP

Script tự động loại bỏ:
- IP private (192.168.x.x, 10.x.x.x, 172.16-31.x.x)
- IP localhost (127.x.x.x)
- IP không hợp lệ (0.x.x.x, ***************)
- IP duplicate

## Xử lý CAPTCHA

Khi gặp CAPTCHA:
1. Script sẽ dừng và thông báo
2. Bạn cần nhìn vào cửa sổ browser
3. Giải CAPTCHA thủ công
4. Nhấn Enter trong terminal để tiếp tục

## Troubleshooting

### Lỗi WebDriver:
```bash
# Cài đặt lại Chrome WebDriver
pip install --upgrade webdriver-manager
```

### Lỗi CAPTCHA liên tục:
- Sử dụng VPN khác
- Đợi một thời gian rồi thử lại
- Sử dụng script đơn giản với HTML đã tải sẵn

### Không tìm thấy IP:
- Kiểm tra trang web có thay đổi cấu trúc không
- Thử với script đơn giản
- Kiểm tra kết nối internet

## Ví dụ sử dụng

### Lọc IP từ file HTML có sẵn:
```bash
# Tải HTML từ browser và lưu thành file
# Sau đó chạy:
python simple_ip_filter.py
# Chọn option 2, nhập đường dẫn file HTML
```

### Kiểm tra trạng thái IP:
```bash
python simple_ip_filter.py
# Chọn option 3, nhập file danh sách IP
# Chọn có kiểm tra trạng thái
```

## Lưu ý

- Script tuân thủ robots.txt và rate limiting
- Không spam request đến server
- Chỉ sử dụng cho mục đích nghiên cứu/học tập
- Tôn trọng terms of service của website
