import requests
import hashlib
import uuid
import re
import functools
reg = {
    "realm": re.compile(r'realm="(.*?)"'),
    "nonce": re.compile(r'nonce="(.*?)"'),
}
def find(var: str, response: str):
    """Searches for `var` in `response`."""
    match = reg[var].search(response)
    if match:
        return match.group(1)
    else:
        return ""
def calculate_response(username, password, realm, nonce, uri):
    cnonce = uuid.uuid4().hex
    ncvalue = '00000001'
    qop = 'auth'

    hash1 = hashlib.md5(f'{username}:{realm}:{password}'.encode()).hexdigest()
    hash2 = hashlib.md5(f'GET:{uri}'.encode()).hexdigest()
    response = hashlib.md5(f'{hash1}:{nonce}:{ncvalue}:{cnonce}:{qop}:{hash2}'.encode()).hexdigest()

    return response, cnonce, ncvalue

def login(ip,username, password, realm, nonce, uri,response):
    response, cnonce, ncvalue = calculate_response(username, password, realm, nonce, uri)
    auth_header = (
        "Authorization: Digest "
        f'username="{username}", '
        f'realm="{realm}", '
        f'nonce="{nonce}", '
        f'uri="{uri}", '
        f'nc="{ncvalue}", '
        f'cnonce="{cnonce}", '
        f'qop="auth", '
        f'response="{response}"'
    )
    print(f'login1 = {auth_header}')
    auth_header = {
        'Authorization': f'Digest username="{username}", realm="{realm}", nonce="{nonce}", uri="{uri}", nc={ncvalue}, cnonce="{cnonce}", qop="auth", response="{response}"'
    }

    url = f'http://{ip}/{uri}'
    print(f'login = {auth_header}')
    response = requests.get(url, headers=auth_header)

    # Xử lý response
    print(f'bbbbbb = {response.text}')
    if response.status_code == 200:
        print("Đăng nhập thành công!")
    else:
        print("Đăng nhập thất bại.")



    # login(username, password, realm, nonce, uri)
def get_http_response(ip, port):
    url = f"http://{ip}:{port}"
    url = f"http://{ip}/cgi-bin/global.login?userName=admin"
    try:
        response = requests.get(url)
        
        if response.status_code == 200:
            print("HTTP/1.1 200 OK")
            print(response.headers)
            print("Content:")
            print(response.text)
            return None
        else:
            print(f"HTTP request không thành công. Mã trạng thái: {response.status_code}")
            print(response.headers)
            auth_header = response.headers['WWW-Authenticate']
            realm = re.search(r'realm="([^"]+)"', auth_header).group(1)
            nonce = re.search(r'nonce="([^"]+)"', auth_header).group(1)

            print("Realm:", realm)
            print("Nonce:", nonce)
            # realm = find("realm", response.headers)
            # nonce = find("nonce", response.headers)
            return (realm,nonce)
        
    except requests.ConnectionError:
        print("Không thể kết nối đến địa chỉ IP và cổng đã cung cấp.")
        return None

# Địa chỉ IP và cổng bạn muốn kiểm tra
# ip = "*************"
# port = 554  # Cổng 80

# get_http_response(ip, port)
@functools.lru_cache()
def _ha1(username, realm, password):
    return hashlib.md5(f"{username}:{realm}:{password}".encode("ascii")).hexdigest()

if __name__ == "__main__":
    ip = "*************"
    port = 554  # Cổng 80

    username = "admin"
    password = "abcd1234"
    realm, nonce = get_http_response(ip, port)
    print(f'aaaaaaaa = {realm}')
    # realm = "DH_00408CA5EA04"
    # nonce = "000562fdY631973ef04f77a3ede7c1832ff48720ef95ad"

    uri = "cgi-bin/global.login?userName=admin"
    HA1 = _ha1(username, realm, password)
    HA2 = hashlib.md5(f"DESCRIBE:{uri}".encode("ascii")).hexdigest()
    response = hashlib.md5(f"{HA1}:{nonce}:{HA2}".encode("ascii")).hexdigest()
    login(ip,username, password, realm, nonce, uri,response)