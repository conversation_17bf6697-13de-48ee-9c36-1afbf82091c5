import ast

# File name
file_name = "ASI_result.txt"

# Initialize storage
devices = []

try:
    # Read and process each line as a Python dictionary
    with open(file_name, "r", encoding="utf-8") as file:
        for line in file:
            line = line.strip()
            if line:  # Skip empty lines
                try:
                    device = ast.literal_eval(line)  # Safely evaluate the string as a Python dictionary
                    devices.append(device)
                except (SyntaxError, ValueError) as e:
                    print(f"Skipping invalid line: {line}\nError: {e}")
except FileNotFoundError:
    print(f"Error: The file '{file_name}' was not found.")
    devices = []

# Initialize counters
total_cameras = 0
brand_camera_count = {}

if devices:
    # Process each device
    for device in devices:
        # Get the number of cameras and brand
        num_cameras = device.get('number_cameras', 0)
        brand = device.get('brand', 'Unknown')
        
        # Add number of cameras to total
        total_cameras += num_cameras
        
        # Aggregate camera count by brand
        if brand not in brand_camera_count:
            brand_camera_count[brand] = 0
        brand_camera_count[brand] += num_cameras

    # Print results
    print("Total number of cameras:", total_cameras)
    print("Camera count by brand:")
    for brand, count in brand_camera_count.items():
        print(f"  {brand}: {count}")
else:
    print("No valid devices found.")
