# i have file ASI.txt with content:
# *************
# **************
# ************
#this file is all ip

# and file filter_ip_alive.txt with content:
# *************
# **************
# ************
#this file is all ip alive

# need create file excel contain ip and status of ip is alive or dead
import openpyxl
import os
from openpyxl.styles import Font, Alignment
from openpyxl.styles import PatternFill

# get all ip from file filter_ip_alive.txt
def get_ip_from_file(file_path):
    ips = []
    with open(file_path, "r") as file:
        for line in file:
            line = line.strip()
            if line:  # Skip empty lines
                ips.append(line)
    return ips


all_ip = get_ip_from_file("ASI.txt")
alive_ip = get_ip_from_file("filtered_ips_alive.txt")
dead_ip = list(set(all_ip) - set(alive_ip))

# Create a new workbook
workbook = openpyxl.Workbook()
sheet = workbook.active
sheet.title = "IP Status"

# Set column headers
headers = ["IP Address", "Status"]
for i, header in enumerate(headers, start=1):
    sheet.cell(row=1, column=i, value=header)

# Set column width
sheet.column_dimensions["A"].width = 40
sheet.column_dimensions["B"].width = 20

# Set header styles
header_font = Font(bold=True)
header_alignment = Alignment(horizontal="center", vertical="center")
header_fill = PatternFill(start_color="00FF00", end_color="00FF00", fill_type="solid")
for cell in sheet["1:1"]:
    cell.font = header_font
    cell.alignment = header_alignment
    cell.fill = header_fill

# Write IP addresses and status to the sheet
for i, ip in enumerate(all_ip, start=2):
    # highlight red if ip is dead
    fill = PatternFill(start_color="FF0000", end_color="FF0000", fill_type="solid")
    if ip in alive_ip:
        status = "Alive"
        fill = PatternFill(start_color="00FF00", end_color="00FF00", fill_type="solid")
    else:
        status = "Dead"
    sheet.cell(row=i, column=1, value=ip)
    sheet.cell(row=i, column=2, value=status)
    sheet.cell(row=i, column=2).fill = fill
    

# Save the workbook
workbook.save("IP_Status.xlsx")

# Check if the file was created
if os.path.exists("IP_Status.xlsx"):
    print("IP_Status.xlsx was successfully created.")
else:
    print("An error occurred while creating IP_Status.xlsx.")


