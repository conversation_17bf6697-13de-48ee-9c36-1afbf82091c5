import argparse
import os
import threading
from pathlib import Path
import time
from cam_scan.cam_scan import CamScan
import subprocess
from screeninfo import get_monitors

# Configuration
CHUNK_SIZE = 50  # Number of lines per split file
INPUT_FILE = Path.cwd() / "all_vn.txt"
RESULT_FILE = Path.cwd() / "result.txt"
TEMP_DIR = Path.cwd() / "temp_chunks"  # Directory to store split files

# Prepare result file and temp directory
if os.path.exists(RESULT_FILE):
    with RESULT_FILE.open("w") as f:
        f.truncate(0)  # Clear existing contents

if not TEMP_DIR.exists():
    TEMP_DIR.mkdir(parents=True)

# Function to split the input file
def split_file(input_file, chunk_size, temp_dir):
    with input_file.open("r") as f:
        lines = f.readlines()
    
    chunks = [
        lines[i:i + chunk_size] for i in range(0, len(lines), chunk_size)
    ]
    
    chunk_files = []
    for idx, chunk in enumerate(chunks):
        chunk_file = temp_dir / f"chunk_{idx}.txt"
        with chunk_file.open("w") as cf:
            cf.writelines(chunk)
        chunk_files.append(chunk_file)
    
    return chunk_files

# Callback to write results
def callback_result(data):
    if data['state']:
        result_file.write(f"{data}\n")

# Function to run a CamScan for a single chunk
def run_cam_scan(chunk_file):
    options = {
        "file_path": chunk_file,
        "callback_result": callback_result,
    }
    cam_scan = CamScan(options=options)
    cam_scan.scan()

def get_screen_resolution():
    # Get screen resolution using screeninfo library (cross-platform)
    monitor = get_monitors()[0]  # Get the primary monitor
    screen_width, screen_height = monitor.width, monitor.height
    return screen_width, screen_height

def open_terminal_grid(rows=3, cols=3):
    # Get screen resolution dynamically
    screen_width, screen_height = get_screen_resolution()

    # Calculate window width and height based on the grid size
    window_width = screen_width // cols
    window_height = screen_height // rows

    print(f"Screen resolution: {screen_width}x{screen_height}")
    print(f"Window size: {window_width}x{window_height}")

    terminal_ids = []  # List to store terminal window IDs
    
    # Loop through the grid to open terminals in each position
    for row in range(1, rows + 1):
        for col in range(1, cols + 1):
            # Calculate the position for each terminal window
            left = (col - 1) * window_width
            top = (row - 1) * window_height

            # Construct the AppleScript to open the Terminal window and set its position
            osascript_command = f"""
            osascript -e '
            tell application "Terminal"
                -- Open a new window
                do script ""
                -- Set the window size and position on the screen
                set windowBounds to {{ {left}, {top}, {left + window_width}, {top + window_height} }}
                set bounds of front window to windowBounds
                -- Get the window ID of the front window
                set terminal_id to id of front window
                return terminal_id
            end tell'
            """
            # Capture the terminal ID
            result = subprocess.run(osascript_command, capture_output=True, text=True, shell=True)
            terminal_id = result.stdout.strip()
            print(f"Opened terminal with ID: {terminal_id}")
            terminal_ids.append(terminal_id)  # Store the terminal ID

    return terminal_ids

def send_command_to_terminal(terminal_id, command):
    """Send a command to a specific Terminal window identified by terminal_id."""
    print(f"Sending command to Terminal {terminal_id}: {command}")
    osascript_command = """
    osascript -e '
    tell application "Terminal"
        set myWindow to first window whose id is %s
        do script "%s" in myWindow
    end tell'
    """ % (terminal_id, command)
    subprocess.run(osascript_command, shell=True)

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Process CamScan tasks.")
    parser.add_argument(
        "--chunk-file", 
        type=str, 
        help="Path to a single chunk file to process with CamScan."
    )
    args = parser.parse_args()


    global result_file
    try:
        # Open the file in append mode
        result_file = open(RESULT_FILE, "a")
        print(f"File {RESULT_FILE} opened successfully.")
    except Exception as e:
        print(f"Error opening file: {e}")

    

    # If a chunk file is provided, run the scan for that chunk
    if args.chunk_file:
        print(f'args.chunk_file: {args.chunk_file}')
        run_cam_scan(args.chunk_file)
    else:
        # Main script logic to split and spawn tasks
        start_time = time.time()

        # Open a grid of terminal windows and capture their IDs
        terminal_ids = open_terminal_grid(5, 5)

        # Split the input file into chunks
        chunk_files = split_file(INPUT_FILE, CHUNK_SIZE, TEMP_DIR)

        # Spawn terminals for each chunk and get their window IDs
        threads = []
        for i, chunk_file in enumerate(chunk_files):
            # Construct the commands to run in each terminal
            current_dir = Path.cwd()
            env_path = current_dir / "env/bin/activate"
            python_executable = "python"  # Adjust to "python3" if needed
            script_path = __file__  # Path to the current script

            # Construct the commands to run step by step
            commands = [
                f'cd {current_dir}',  # Change to the working directory
                f'source {env_path}',  # Activate the virtual environment
                f'{python_executable} {script_path} --chunk-file {chunk_file}'  # Run the script
            ]

            # Combine the commands into a single script
            terminal_command = "; ".join(commands)
            print(f'Terminal command: {terminal_command}')  # Debug: print the full command

            # Send the command to the corresponding terminal
            t = threading.Thread(target=lambda i=i: send_command_to_terminal(
                terminal_ids[i], terminal_command
            ))
            t.start()
            threads.append(t)

        # Wait for all threads to finish
        for t in threads:
            t.join()

        end_time = time.time()
        print(f"Processing completed in {end_time - start_time} seconds.")
