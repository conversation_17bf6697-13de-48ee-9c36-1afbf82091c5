#{'ip': '*************', 'user': 'admin', 'password': 'abcd1234', 'port': 554, 'brand': 'Hikvision', 'model': 'DS-7116HGHI-F1', 'mac_address': '54:C4:15:D9:16:CD', 'serial_number': 'DS-7116HGHI-F11620170320CCWR733955367WCVU', 'nvr': None, 'number_cameras': 16, 'device_type': '', 'device_name': 'Embedded Net DVR', 'cam_cfg': [{'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@*************:554/Streaming/Channels/101/', 'rtsp://admin:abcd1234@*************:554/Streaming/Channels/102/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@*************:554/Streaming/Channels/201/', 'rtsp://admin:abcd1234@*************:554/Streaming/Channels/202/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@*************:554/Streaming/Channels/301/', 'rtsp://admin:abcd1234@*************:554/Streaming/Channels/302/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@*************:554/Streaming/Channels/401/', 'rtsp://admin:abcd1234@*************:554/Streaming/Channels/402/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@*************:554/Streaming/Channels/501/', 'rtsp://admin:abcd1234@*************:554/Streaming/Channels/502/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@*************:554/Streaming/Channels/601/', 'rtsp://admin:abcd1234@*************:554/Streaming/Channels/602/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@*************:554/Streaming/Channels/701/', 'rtsp://admin:abcd1234@*************:554/Streaming/Channels/702/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@*************:554/Streaming/Channels/801/', 'rtsp://admin:abcd1234@*************:554/Streaming/Channels/802/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@*************:554/Streaming/Channels/901/', 'rtsp://admin:abcd1234@*************:554/Streaming/Channels/902/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@*************:554/Streaming/Channels/1001/', 'rtsp://admin:abcd1234@*************:554/Streaming/Channels/1002/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@*************:554/Streaming/Channels/1101/', 'rtsp://admin:abcd1234@*************:554/Streaming/Channels/1102/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@*************:554/Streaming/Channels/1201/', 'rtsp://admin:abcd1234@*************:554/Streaming/Channels/1202/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@*************:554/Streaming/Channels/1301/', 'rtsp://admin:abcd1234@*************:554/Streaming/Channels/1302/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@*************:554/Streaming/Channels/1401/', 'rtsp://admin:abcd1234@*************:554/Streaming/Channels/1402/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@*************:554/Streaming/Channels/1501/', 'rtsp://admin:abcd1234@*************:554/Streaming/Channels/1502/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@*************:554/Streaming/Channels/1601/', 'rtsp://admin:abcd1234@*************:554/Streaming/Channels/1602/']}], 'state': True, 'no': 19, 'total': 89, 'records': [], 'metadata': []}
#{'ip': '**************', 'user': 'admin', 'password': 'abcd1234', 'port': 554, 'brand': 'Hikvision', 'model': 'DS-7216HUHI-F2%2FN', 'mac_address': 'BC:AD:28:97:D1:F2', 'serial_number': 'DS-7216HUHI-F2/N1620160614CCWR606431003WCVU', 'nvr': None, 'number_cameras': 16, 'device_type': '', 'device_name': 'Embedded Net DVR', 'cam_cfg': [{'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/Streaming/Channels/101/', 'rtsp://admin:abcd1234@**************:554/Streaming/Channels/102/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/Streaming/Channels/201/', 'rtsp://admin:abcd1234@**************:554/Streaming/Channels/202/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/Streaming/Channels/301/', 'rtsp://admin:abcd1234@**************:554/Streaming/Channels/302/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/Streaming/Channels/401/', 'rtsp://admin:abcd1234@**************:554/Streaming/Channels/402/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/Streaming/Channels/501/', 'rtsp://admin:abcd1234@**************:554/Streaming/Channels/502/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/Streaming/Channels/601/', 'rtsp://admin:abcd1234@**************:554/Streaming/Channels/602/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/Streaming/Channels/701/', 'rtsp://admin:abcd1234@**************:554/Streaming/Channels/702/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/Streaming/Channels/801/', 'rtsp://admin:abcd1234@**************:554/Streaming/Channels/802/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/Streaming/Channels/901/', 'rtsp://admin:abcd1234@**************:554/Streaming/Channels/902/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/Streaming/Channels/1001/', 'rtsp://admin:abcd1234@**************:554/Streaming/Channels/1002/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/Streaming/Channels/1101/', 'rtsp://admin:abcd1234@**************:554/Streaming/Channels/1102/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/Streaming/Channels/1201/', 'rtsp://admin:abcd1234@**************:554/Streaming/Channels/1202/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/Streaming/Channels/1301/', 'rtsp://admin:abcd1234@**************:554/Streaming/Channels/1302/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/Streaming/Channels/1401/', 'rtsp://admin:abcd1234@**************:554/Streaming/Channels/1402/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/Streaming/Channels/1501/', 'rtsp://admin:abcd1234@**************:554/Streaming/Channels/1502/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/Streaming/Channels/1601/', 'rtsp://admin:abcd1234@**************:554/Streaming/Channels/1602/']}], 'state': True, 'no': 20, 'total': 89, 'records': [], 'metadata': []}


# write script get only ip from result file .txt then write to new file
import ast

def filter_ips_with_realmonitor(file_path):
    filtered_ips = []
    with open(file_path, 'r') as file:
        for i, line in enumerate(file, start=1):
            try:
                # Use ast.literal_eval for Python-style data
                device = ast.literal_eval(line.strip())
                filtered_ips.append(device["ip"])
            except (ValueError, SyntaxError) as e:
                print(f"Line {i} is invalid: {line.strip()}")
                print(f"Error: {e}")
                continue
    return filtered_ips

file_path = "result2.txt"
ips = filter_ips_with_realmonitor(file_path)

#writing to file
with open("filter_ip_nvr.txt", "w") as file:
    for ip in ips:
        file.write(f"{ip}\n")

