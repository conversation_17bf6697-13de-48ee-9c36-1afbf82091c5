#!/usr/bin/env python3
"""
Script để lọc IP từ trang web tracemyip.org cho tỉnh Quảng Ninh
Yêu cầu: selenium, beautifulsoup4, requests
"""

import re
import time
import json
import csv
from typing import List, Dict, Optional
from urllib.parse import urljoin, urlparse
import requests
from bs4 import BeautifulSoup
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException


class QuangNinhIPFilter:
    def __init__(self, headless: bool = False):
        """
        Khởi tạo IP Filter cho Quảng Ninh
        
        Args:
            headless: Chạy browser ẩn hay không
        """
        self.base_url = "https://tools.tracemyip.org/search--state/tinh+quang+ninh"
        self.headless = headless
        self.driver = None
        self.ip_pattern = re.compile(r'\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b')
        
    def setup_driver(self):
        """Thiết lập Chrome WebDriver"""
        chrome_options = Options()
        if self.headless:
            chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
        
        try:
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.implicitly_wait(10)
            return True
        except Exception as e:
            print(f"Lỗi khởi tạo WebDriver: {e}")
            return False
    
    def solve_captcha_manually(self) -> bool:
        """
        Chờ người dùng giải CAPTCHA thủ công
        """
        print("Phát hiện CAPTCHA. Vui lòng:")
        print("1. Nhìn vào cửa sổ browser")
        print("2. Giải CAPTCHA")
        print("3. Nhấn Enter trong terminal này sau khi hoàn thành")
        
        input("Nhấn Enter sau khi đã giải CAPTCHA...")
        return True
    
    def extract_ips_from_page(self) -> List[Dict]:
        """
        Trích xuất IP từ trang hiện tại
        
        Returns:
            List các dict chứa thông tin IP
        """
        ips_data = []
        
        try:
            # Đợi trang load
            WebDriverWait(self.driver, 10).wait(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # Lấy HTML source
            page_source = self.driver.page_source
            soup = BeautifulSoup(page_source, 'html.parser')
            
            # Tìm tất cả IP trong trang
            text_content = soup.get_text()
            ip_matches = self.ip_pattern.findall(text_content)
            
            # Tìm thông tin chi tiết hơn từ các table hoặc div
            tables = soup.find_all('table')
            for table in tables:
                rows = table.find_all('tr')
                for row in rows:
                    cells = row.find_all(['td', 'th'])
                    row_text = ' '.join([cell.get_text().strip() for cell in cells])
                    
                    # Tìm IP trong row
                    ip_in_row = self.ip_pattern.findall(row_text)
                    for ip in ip_in_row:
                        if self.is_valid_ip(ip):
                            ip_info = {
                                'ip': ip,
                                'context': row_text[:200],  # Lấy 200 ký tự đầu làm context
                                'source': 'table'
                            }
                            ips_data.append(ip_info)
            
            # Tìm IP từ các div khác
            divs = soup.find_all('div')
            for div in divs:
                div_text = div.get_text().strip()
                if len(div_text) > 10:  # Chỉ xử lý div có nội dung
                    ip_in_div = self.ip_pattern.findall(div_text)
                    for ip in ip_in_div:
                        if self.is_valid_ip(ip):
                            ip_info = {
                                'ip': ip,
                                'context': div_text[:200],
                                'source': 'div'
                            }
                            ips_data.append(ip_info)
            
            # Loại bỏ duplicate
            unique_ips = {}
            for ip_info in ips_data:
                ip = ip_info['ip']
                if ip not in unique_ips:
                    unique_ips[ip] = ip_info
            
            return list(unique_ips.values())
            
        except Exception as e:
            print(f"Lỗi khi trích xuất IP: {e}")
            return []
    
    def is_valid_ip(self, ip: str) -> bool:
        """
        Kiểm tra IP có hợp lệ không
        
        Args:
            ip: IP address string
            
        Returns:
            True nếu IP hợp lệ
        """
        try:
            parts = ip.split('.')
            if len(parts) != 4:
                return False
            
            for part in parts:
                num = int(part)
                if num < 0 or num > 255:
                    return False
            
            # Loại bỏ một số IP không hợp lệ
            if ip.startswith('0.') or ip.startswith('127.') or ip == '***************':
                return False
                
            return True
        except:
            return False
    
    def save_results(self, ips_data: List[Dict], output_format: str = 'json'):
        """
        Lưu kết quả ra file
        
        Args:
            ips_data: Danh sách IP data
            output_format: Format output ('json', 'csv', 'txt')
        """
        timestamp = int(time.time())
        
        if output_format == 'json':
            filename = f"quang_ninh_ips_{timestamp}.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(ips_data, f, indent=2, ensure_ascii=False)
        
        elif output_format == 'csv':
            filename = f"quang_ninh_ips_{timestamp}.csv"
            with open(filename, 'w', newline='', encoding='utf-8') as f:
                if ips_data:
                    writer = csv.DictWriter(f, fieldnames=ips_data[0].keys())
                    writer.writeheader()
                    writer.writerows(ips_data)
        
        elif output_format == 'txt':
            filename = f"quang_ninh_ips_{timestamp}.txt"
            with open(filename, 'w', encoding='utf-8') as f:
                for ip_info in ips_data:
                    f.write(f"{ip_info['ip']}\n")
        
        print(f"Đã lưu {len(ips_data)} IP vào file: {filename}")
        return filename
    
    def run(self, output_format: str = 'json') -> Optional[str]:
        """
        Chạy quá trình lọc IP
        
        Args:
            output_format: Format output file
            
        Returns:
            Tên file output hoặc None nếu lỗi
        """
        try:
            # Thiết lập driver
            if not self.setup_driver():
                return None
            
            print(f"Đang truy cập: {self.base_url}")
            self.driver.get(self.base_url)
            
            # Kiểm tra CAPTCHA
            try:
                captcha_element = self.driver.find_element(By.TAG_NAME, "img")
                if "captcha" in captcha_element.get_attribute("src").lower():
                    print("Phát hiện CAPTCHA...")
                    self.solve_captcha_manually()
            except NoSuchElementException:
                print("Không phát hiện CAPTCHA")
            
            # Đợi trang load hoàn toàn
            time.sleep(3)
            
            # Trích xuất IP
            print("Đang trích xuất IP...")
            ips_data = self.extract_ips_from_page()
            
            if ips_data:
                print(f"Tìm thấy {len(ips_data)} IP")
                filename = self.save_results(ips_data, output_format)
                return filename
            else:
                print("Không tìm thấy IP nào")
                return None
                
        except Exception as e:
            print(f"Lỗi trong quá trình chạy: {e}")
            return None
        
        finally:
            if self.driver:
                self.driver.quit()


def main():
    """Hàm main"""
    print("=== Script lọc IP Quảng Ninh ===")
    print("1. JSON format")
    print("2. CSV format") 
    print("3. TXT format (chỉ IP)")
    
    choice = input("Chọn format output (1-3): ").strip()
    format_map = {'1': 'json', '2': 'csv', '3': 'txt'}
    output_format = format_map.get(choice, 'json')
    
    headless_choice = input("Chạy ẩn browser? (y/n): ").strip().lower()
    headless = headless_choice == 'y'
    
    # Khởi tạo và chạy
    filter_tool = QuangNinhIPFilter(headless=headless)
    result_file = filter_tool.run(output_format)
    
    if result_file:
        print(f"Hoàn thành! Kết quả đã được lưu vào: {result_file}")
    else:
        print("Không thể hoàn thành quá trình lọc IP")


if __name__ == "__main__":
    main()
