{'ip': '***************', 'user': 'admin', 'password': 'hd543211', 'port': 554, 'brand': 'Hikvision', 'model': 'DS-2DE5225IW-AE', 'mac_address': '2C:A5:9C:58:8E:40', 'serial_number': 'DS-2DE5225IW-AE20210116AAWRF42855219', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': 'IP DOME', 'cam_cfg': [{'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:hd543211@***************:554/']}], 'state': True, 'no': 7, 'total': 138, 'records': [], 'metadata': []}
{'ip': '*************', 'user': 'admin', 'password': 'hd543211', 'port': 554, 'brand': 'Hikvision', 'model': 'DS-2DE5225IW-AE', 'mac_address': '08:A1:89:C7:79:C9', 'serial_number': 'DS-2DE5225IW-AE20210415AAWRF81274632', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': 'IP DOME', 'cam_cfg': [{'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:hd543211@*************:554/']}], 'state': True, 'no': 8, 'total': 138, 'records': [], 'metadata': []}
{'ip': '************', 'user': 'admin', 'password': 'abcd1234', 'port': 554, 'brand': 'Hikvision', 'model': 'DS-2CD1123G0E-I(L)', 'mac_address': 'BC:5E:33:DC:54:93', 'serial_number': 'DS-2CD1123G0E-I(L)20230228AAWRL34638325', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': 'IP CAMERA', 'cam_cfg': [{'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@************:554/']}], 'state': True, 'no': 9, 'total': 138, 'records': [], 'metadata': []}
{'ip': '***************', 'user': 'admin', 'password': 'abcd1234', 'port': 554, 'brand': 'Hikvision', 'model': 'DS-2CD2T21G1-I', 'mac_address': '24:0F:9B:A0:35:F4', 'serial_number': 'DS-2CD2T21G1-I20211225AAWRJ30938767', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': 'IP CAMERA', 'cam_cfg': [{'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@***************:554/']}], 'state': True, 'no': 10, 'total': 138, 'records': [], 'metadata': []}
{'ip': '***************', 'user': 'admin', 'password': 'hd543211', 'port': 554, 'brand': 'Hikvision', 'model': 'DS-2CD2T21G1-I', 'mac_address': '24:0F:9B:98:EC:DB', 'serial_number': 'DS-2CD2T21G1-I20211221AAWRJ29673330', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': 'IP CAMERA', 'cam_cfg': [{'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:hd543211@***************:554/']}], 'state': True, 'no': 11, 'total': 138, 'records': [], 'metadata': []}
{'ip': '***************', 'user': 'admin', 'password': '1234qwer', 'port': 554, 'brand': 'Hikvision', 'model': 'DS-2CD1321-I', 'mac_address': '94:E1:AC:2E:7C:C1', 'serial_number': 'DS-2CD1321-I20180317AAWR202241100', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': 'IP CAMERA', 'cam_cfg': [{'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:1234qwer@***************:554/']}], 'state': True, 'no': 13, 'total': 138, 'records': [], 'metadata': []}
{'ip': '***************', 'user': 'admin', 'password': 'abcd1234', 'port': 554, 'brand': 'Hikvision', 'model': 'DS-7604NI-K1', 'mac_address': 'EC:C8:9C:BA:A1:D8', 'serial_number': 'DS-7604NI-K10420210219CCRRF54894179WCVU', 'nvr': None, 'number_cameras': 2, 'device_type': '', 'device_name': 'Network Video Recorder', 'cam_cfg': [{'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@***************:554/Streaming/Channels/101/', 'rtsp://admin:abcd1234@***************:554/Streaming/Channels/102/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@***************:554/Streaming/Channels/201/', 'rtsp://admin:abcd1234@***************:554/Streaming/Channels/202/']}], 'state': True, 'no': 14, 'total': 138, 'records': [], 'metadata': []}
{'ip': '***************', 'user': 'admin', 'password': 'abcd1234', 'port': 554, 'brand': 'Hikvision', 'model': 'DS-7604NI-K1(B)', 'mac_address': 'C0:51:7E:5F:63:99', 'serial_number': 'DS-7604NI-K1(B)0420201124CCRRF16753840WCVU', 'nvr': None, 'number_cameras': 2, 'device_type': '', 'device_name': 'Network Video Recorder', 'cam_cfg': [{'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@***************:554/Streaming/Channels/101/', 'rtsp://admin:abcd1234@***************:554/Streaming/Channels/102/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@***************:554/Streaming/Channels/201/', 'rtsp://admin:abcd1234@***************:554/Streaming/Channels/202/']}], 'state': True, 'no': 15, 'total': 138, 'records': [], 'metadata': []}
{'ip': '**************', 'user': 'admin', 'password': 'abcd1234', 'port': 554, 'brand': 'Hikvision', 'model': 'DS-7604NI-K1(B)', 'mac_address': 'C0:51:7E:5F:63:E4', 'serial_number': 'DS-7604NI-K1(B)0420201124CCRRF16753915WCVU', 'nvr': None, 'number_cameras': 2, 'device_type': '', 'device_name': 'Network Video Recorder', 'cam_cfg': [{'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/Streaming/Channels/101/', 'rtsp://admin:abcd1234@**************:554/Streaming/Channels/102/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/Streaming/Channels/201/', 'rtsp://admin:abcd1234@**************:554/Streaming/Channels/202/']}], 'state': True, 'no': 16, 'total': 138, 'records': [], 'metadata': []}
{'ip': '**************', 'user': 'admin', 'password': 'abcd1234', 'port': 554, 'brand': 'Hikvision', 'model': 'DS-7204HGHI-K1', 'mac_address': '3C:1B:F8:13:D1:7C', 'serial_number': 'DS-7204HGHI-K10420231007CCWRAH3611343WCVU', 'nvr': None, 'number_cameras': 4, 'device_type': '', 'device_name': 'Embedded Net DVR', 'cam_cfg': [{'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/Streaming/Channels/101/', 'rtsp://admin:abcd1234@**************:554/Streaming/Channels/102/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/Streaming/Channels/201/', 'rtsp://admin:abcd1234@**************:554/Streaming/Channels/202/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/Streaming/Channels/301/', 'rtsp://admin:abcd1234@**************:554/Streaming/Channels/302/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/Streaming/Channels/401/', 'rtsp://admin:abcd1234@**************:554/Streaming/Channels/402/']}], 'state': True, 'no': 17, 'total': 138, 'records': [], 'metadata': []}
{'ip': '**************', 'user': 'admin', 'password': 'abcd1234', 'port': 554, 'brand': 'Hikvision', 'model': 'DS-7104NI-Q1/M', 'mac_address': '24:28:FD:35:99:E3', 'serial_number': 'DS-7104NI-Q1/M0420210816CCRRG53566116WVU', 'nvr': None, 'number_cameras': 2, 'device_type': '', 'device_name': 'Network Video Recorder', 'cam_cfg': [{'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/Streaming/Channels/101/', 'rtsp://admin:abcd1234@**************:554/Streaming/Channels/102/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/Streaming/Channels/201/', 'rtsp://admin:abcd1234@**************:554/Streaming/Channels/202/']}], 'state': True, 'no': 18, 'total': 138, 'records': [], 'metadata': []}
{'ip': '***************', 'user': 'admin', 'password': '12345', 'port': 554, 'brand': 'Hikvision', 'model': None, 'mac_address': None, 'serial_number': None, 'nvr': None, 'number_cameras': 0, 'device_type': None, 'device_name': None, 'cam_cfg': [{'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:12345@***************:554/']}], 'state': True, 'no': 19, 'total': 138, 'records': [], 'metadata': {'firmwareVersion': None, 'firmwareReleasedDate': None, 'encoderVersion': None, 'encoderReleasedDate': None, 'telecontrolID': None, 'customizedInfo': None}}
{'ip': '***********', 'user': 'admin', 'password': 'abcd1234', 'port': 554, 'brand': 'Hikvision', 'model': 'DS-7604NI-K1/4P', 'mac_address': '24:0f:9b:05:be:62', 'serial_number': 'DS-7604NI-K1/4P0420211209CCRRJ21159060WCVU', 'nvr': True, 'number_cameras': 2, 'device_type': 'NVR', 'device_name': 'Network Video Recorder', 'cam_cfg': [{'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': 'HIKVISION', 'camera_model': None, 'firmware_version': None, 'serial_number': None, 'ip_address': '*************', 'rtsp_list': ['rtsp://admin:abcd1234@***********/Streaming/Channels/101', 'rtsp://admin:abcd1234@***********/Streaming/Channels/102']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': 'HIKVISION', 'camera_model': None, 'firmware_version': None, 'serial_number': None, 'ip_address': '*************', 'rtsp_list': ['rtsp://admin:abcd1234@***********/Streaming/Channels/201', 'rtsp://admin:abcd1234@***********/Streaming/Channels/202']}], 'state': True, 'no': 20, 'total': 138, 'records': [{'link': 'rtsp://admin:abcd1234@***********/Streaming/Channels/101', 'start_time': '2024-11-20T00:22:34Z', 'end_time': '2024-11-20T01:41:39Z', 'record': 'rtsp://admin:abcd1234@***********:81/Streaming/tracks/201/?starttime=20241120T002234Z&endtime=20241120T014139Z&name=00000000479000000&size=1064102948'}, {'link': 'rtsp://admin:abcd1234@***********/Streaming/Channels/201', 'start_time': '2024-11-20T01:41:39Z', 'end_time': '2024-11-20T03:00:43Z', 'record': 'rtsp://admin:abcd1234@***********:81/Streaming/tracks/201/?starttime=20241120T014139Z&endtime=20241120T030043Z&name=00000000481000000&size=1064100048'}], 'metadata': {'firmwareVersion': 'V4.32.110', 'firmwareReleasedDate': 'build 211009', 'encoderVersion': 'V5.0', 'encoderReleasedDate': 'build 211008', 'telecontrolID': '255', 'customizedInfo': None}}
{'ip': '***************', 'user': 'admin', 'password': '123456', 'port': 554, 'brand': None, 'model': None, 'mac_address': None, 'serial_number': None, 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [{'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:123456@***************:554/']}], 'state': True, 'no': 21, 'total': 138, 'records': [], 'metadata': []}
{'ip': '**********', 'user': 'admin', 'password': 'abcd1234', 'port': 554, 'brand': 'Hikvision', 'model': 'DS-7604NI-K1/4P', 'mac_address': '24:0f:9b:05:be:b2', 'serial_number': 'DS-7604NI-K1/4P0420211208CCRRJ21159140WCVU', 'nvr': True, 'number_cameras': 2, 'device_type': 'NVR', 'device_name': 'Network Video Recorder', 'cam_cfg': [{'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': 'HIKVISION', 'camera_model': None, 'firmware_version': None, 'serial_number': None, 'ip_address': '*************', 'rtsp_list': ['rtsp://admin:abcd1234@**********/Streaming/Channels/101', 'rtsp://admin:abcd1234@**********/Streaming/Channels/102']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': 'HIKVISION', 'camera_model': None, 'firmware_version': None, 'serial_number': None, 'ip_address': '*************', 'rtsp_list': ['rtsp://admin:abcd1234@**********/Streaming/Channels/201', 'rtsp://admin:abcd1234@**********/Streaming/Channels/202']}], 'state': True, 'no': 22, 'total': 138, 'records': [{'link': 'rtsp://admin:abcd1234@**********/Streaming/Channels/101', 'start_time': '2024-11-20T00:40:37Z', 'end_time': '2024-11-20T01:59:41Z', 'record': 'rtsp://admin:abcd1234@**********:81/Streaming/tracks/201/?starttime=20241120T004037Z&endtime=20241120T015941Z&name=00000000129000000&size=1064109528'}, {'link': 'rtsp://admin:abcd1234@**********/Streaming/Channels/201', 'start_time': '2024-11-20T01:59:41Z', 'end_time': '2024-11-20T03:18:45Z', 'record': 'rtsp://admin:abcd1234@**********:81/Streaming/tracks/201/?starttime=20241120T015941Z&endtime=20241120T031845Z&name=00000000131000000&size=1064107352'}], 'metadata': {'firmwareVersion': 'V4.32.110', 'firmwareReleasedDate': 'build 211009', 'encoderVersion': 'V5.0', 'encoderReleasedDate': 'build 211008', 'telecontrolID': '255', 'customizedInfo': None}}
{'ip': '***************', 'user': 'admin', 'password': 'abcd1234', 'port': 554, 'brand': 'Dahua', 'model': 'DH-IPC-HDWP3158EIZ5E', 'mac_address': 'e4:24:6c:65:48:9a', 'serial_number': '8D00C38PAGAEBA1', 'nvr': None, 'number_cameras': 1, 'device_type': 'IPC', 'device_name': '8D00C38PAGAEBA1', 'cam_cfg': [{'profileToken': 'MediaProfile00000', 'supportedMainResolution': '[{"width": 3840, "height": 2160}, {"width": 3072, "height": 2048}, {"width": 3072, "height": 1728}, {"width": 2592, "height": 1944}, {"width": 2688, "height": 1520}, {"width": 2304, "height": 1296}, {"width": 1920, "height": 1080}, {"width": 1280, "height": 960}, {"width": 1280, "height": 720}]', 'mainstreamResolution': '3840x2160', 'supportedMainFps': '[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]', 'mainstreamFps': '15', 'rtsp_list': ['rtsp://admin:abcd1234@***************:554/cam/realmonitor?channel=1&subtype=0&unicast=true&proto=Onvif', 'rtsp://admin:abcd1234@***************:554/cam/realmonitor?channel=1&subtype=1&unicast=true&proto=Onvif'], 'is_ptz': True, 'supportedSubResolution': '[{"width": 1920, "height": 1080}, {"width": 1280, "height": 720}, {"width": 704, "height": 576}]', 'substreamResolution': '704x576', 'supportedSubFps': '[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]', 'substreamFps': '15'}], 'state': True, 'no': 23, 'total': 138, 'records': [{'link': 'rtsp://***************:554/cam/realmonitor?channel=1&subtype=0', 'start_time': '2024-11-20T00:00:00Z', 'end_time': '2024-11-20T23:59:59Z', 'record': 'rtsp://admin:abcd1234@***************:554/cam/playback?channel=1&starttime=2024_11_20_00_00_00&endtime=2024_11_20_23_59_59'}], 'metadata': []}
{'ip': '**************', 'user': '', 'password': '', 'port': 554, 'brand': None, 'model': None, 'mac_address': None, 'serial_number': None, 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [{'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://**************:554/']}], 'state': True, 'no': 24, 'total': 138, 'records': [], 'metadata': []}
{'ip': '**************', 'user': 'admin', 'password': 'abcd1234', 'port': 554, 'brand': 'Dahua', 'model': 'DH-XVR4116HS-I-VN', 'mac_address': None, 'serial_number': None, 'nvr': None, 'number_cameras': 16, 'device_type': '', 'device_name': '', 'cam_cfg': [{'profileToken': 'MediaProfile00000', 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/cam/realmonitor?channel=1&subtype=0&unicast=true&proto=Onvif', 'rtsp://admin:abcd1234@**************:554/cam/realmonitor?channel=1&subtype=1&unicast=true&proto=Onvif'], 'is_ptz': True}, {'profileToken': 'MediaProfile00100', 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/cam/realmonitor?channel=2&subtype=0&unicast=true&proto=Onvif', 'rtsp://admin:abcd1234@**************:554/cam/realmonitor?channel=2&subtype=1&unicast=true&proto=Onvif'], 'is_ptz': True}, {'profileToken': 'MediaProfile00200', 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/cam/realmonitor?channel=3&subtype=0&unicast=true&proto=Onvif', 'rtsp://admin:abcd1234@**************:554/cam/realmonitor?channel=3&subtype=1&unicast=true&proto=Onvif'], 'is_ptz': True}, {'profileToken': 'MediaProfile00300', 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/cam/realmonitor?channel=4&subtype=0&unicast=true&proto=Onvif', 'rtsp://admin:abcd1234@**************:554/cam/realmonitor?channel=4&subtype=1&unicast=true&proto=Onvif'], 'is_ptz': True}, {'profileToken': 'MediaProfile00400', 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/cam/realmonitor?channel=5&subtype=0&unicast=true&proto=Onvif', 'rtsp://admin:abcd1234@**************:554/cam/realmonitor?channel=5&subtype=1&unicast=true&proto=Onvif'], 'is_ptz': True}, {'profileToken': 'MediaProfile00500', 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/cam/realmonitor?channel=6&subtype=0&unicast=true&proto=Onvif', 'rtsp://admin:abcd1234@**************:554/cam/realmonitor?channel=6&subtype=1&unicast=true&proto=Onvif'], 'is_ptz': True}, {'profileToken': 'MediaProfile00600', 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/cam/realmonitor?channel=7&subtype=0&unicast=true&proto=Onvif', 'rtsp://admin:abcd1234@**************:554/cam/realmonitor?channel=7&subtype=1&unicast=true&proto=Onvif'], 'is_ptz': True}, {'profileToken': 'MediaProfile00700', 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/cam/realmonitor?channel=8&subtype=0&unicast=true&proto=Onvif', 'rtsp://admin:abcd1234@**************:554/cam/realmonitor?channel=8&subtype=1&unicast=true&proto=Onvif'], 'is_ptz': True}, {'profileToken': 'MediaProfile00800', 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/cam/realmonitor?channel=9&subtype=0&unicast=true&proto=Onvif', 'rtsp://admin:abcd1234@**************:554/cam/realmonitor?channel=9&subtype=1&unicast=true&proto=Onvif'], 'is_ptz': True}, {'profileToken': 'MediaProfile00900', 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/cam/realmonitor?channel=10&subtype=0&unicast=true&proto=Onvif', 'rtsp://admin:abcd1234@**************:554/cam/realmonitor?channel=10&subtype=1&unicast=true&proto=Onvif'], 'is_ptz': True}, {'profileToken': 'MediaProfile01000', 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/cam/realmonitor?channel=11&subtype=0&unicast=true&proto=Onvif', 'rtsp://admin:abcd1234@**************:554/cam/realmonitor?channel=11&subtype=1&unicast=true&proto=Onvif'], 'is_ptz': True}, {'profileToken': 'MediaProfile01100', 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/cam/realmonitor?channel=12&subtype=0&unicast=true&proto=Onvif', 'rtsp://admin:abcd1234@**************:554/cam/realmonitor?channel=12&subtype=1&unicast=true&proto=Onvif'], 'is_ptz': True}, {'profileToken': 'MediaProfile01200', 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/cam/realmonitor?channel=13&subtype=0&unicast=true&proto=Onvif', 'rtsp://admin:abcd1234@**************:554/cam/realmonitor?channel=13&subtype=1&unicast=true&proto=Onvif'], 'is_ptz': True}, {'profileToken': 'MediaProfile01300', 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/cam/realmonitor?channel=14&subtype=0&unicast=true&proto=Onvif', 'rtsp://admin:abcd1234@**************:554/cam/realmonitor?channel=14&subtype=1&unicast=true&proto=Onvif'], 'is_ptz': True}, {'profileToken': 'MediaProfile01400', 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/cam/realmonitor?channel=15&subtype=0&unicast=true&proto=Onvif', 'rtsp://admin:abcd1234@**************:554/cam/realmonitor?channel=15&subtype=1&unicast=true&proto=Onvif'], 'is_ptz': True}, {'profileToken': 'MediaProfile01500', 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/cam/realmonitor?channel=16&subtype=0&unicast=true&proto=Onvif', 'rtsp://admin:abcd1234@**************:554/cam/realmonitor?channel=16&subtype=1&unicast=true&proto=Onvif'], 'is_ptz': True}], 'state': True, 'no': 25, 'total': 138, 'records': [], 'metadata': []}
{'ip': '**************', 'user': 'admin', 'password': 'abcd1234', 'port': 554, 'brand': 'Hikvision', 'model': 'DS-7104HQHI-F1/N', 'mac_address': 'BC:AD:28:E3:7D:B9', 'serial_number': 'DS-7104HQHI-F1/N0420160727CCWR630083033WCVU', 'nvr': None, 'number_cameras': 4, 'device_type': '', 'device_name': 'Victory', 'cam_cfg': [{'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/Streaming/Channels/101/', 'rtsp://admin:abcd1234@**************:554/Streaming/Channels/102/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/Streaming/Channels/201/', 'rtsp://admin:abcd1234@**************:554/Streaming/Channels/202/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/Streaming/Channels/301/', 'rtsp://admin:abcd1234@**************:554/Streaming/Channels/302/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/Streaming/Channels/401/', 'rtsp://admin:abcd1234@**************:554/Streaming/Channels/402/']}], 'state': True, 'no': 26, 'total': 138, 'records': [], 'metadata': []}
{'ip': '**************', 'user': '888888', 'password': '888888', 'port': 554, 'brand': None, 'model': None, 'mac_address': None, 'serial_number': None, 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [{'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://888888:888888@**************:554/']}], 'state': True, 'no': 27, 'total': 138, 'records': [], 'metadata': []}
{'ip': '**************', 'user': 'admin', 'password': 'abcd1234', 'port': 554, 'brand': 'Dahua', 'model': 'DH-XVR1A04', 'mac_address': None, 'serial_number': None, 'nvr': None, 'number_cameras': 4, 'device_type': '', 'device_name': '', 'cam_cfg': [{'profileToken': 'MediaProfile00000', 'supportedMainResolution': '[{"width": 704, "height": 576}, {"width": 352, "height": 576}, {"width": 704, "height": 288}, {"width": 352, "height": 288}, {"width": 176, "height": 144}, {"width": 960, "height": 576}, {"width": 960, "height": 1080}]', 'mainstreamResolution': '960x1080', 'supportedMainFps': '[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25]', 'mainstreamFps': '25', 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/cam/realmonitor?channel=1&subtype=0&unicast=true&proto=Onvif', 'rtsp://admin:abcd1234@**************:554/cam/realmonitor?channel=1&subtype=1&unicast=true&proto=Onvif'], 'is_ptz': None, 'supportedSubResolution': '[{"width": 352, "height": 288}, {"width": 176, "height": 144}]', 'substreamResolution': '352x288', 'supportedSubFps': '[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]', 'substreamFps': '15'}, {'profileToken': 'MediaProfile00100', 'supportedMainResolution': '[{"width": 704, "height": 576}, {"width": 352, "height": 576}, {"width": 704, "height": 288}, {"width": 352, "height": 288}, {"width": 176, "height": 144}, {"width": 960, "height": 576}, {"width": 960, "height": 1080}]', 'mainstreamResolution': '960x1080', 'supportedMainFps': '[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25]', 'mainstreamFps': '25', 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/cam/realmonitor?channel=2&subtype=0&unicast=true&proto=Onvif', 'rtsp://admin:abcd1234@**************:554/cam/realmonitor?channel=2&subtype=1&unicast=true&proto=Onvif'], 'is_ptz': None, 'supportedSubResolution': '[{"width": 352, "height": 288}, {"width": 176, "height": 144}]', 'substreamResolution': '352x288', 'supportedSubFps': '[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]', 'substreamFps': '15'}, {'profileToken': 'MediaProfile00200', 'supportedMainResolution': '[{"width": 704, "height": 576}, {"width": 352, "height": 576}, {"width": 704, "height": 288}, {"width": 352, "height": 288}, {"width": 176, "height": 144}, {"width": 960, "height": 576}, {"width": 960, "height": 1080}]', 'mainstreamResolution': '960x1080', 'supportedMainFps': '[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25]', 'mainstreamFps': '25', 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/cam/realmonitor?channel=3&subtype=0&unicast=true&proto=Onvif', 'rtsp://admin:abcd1234@**************:554/cam/realmonitor?channel=3&subtype=1&unicast=true&proto=Onvif'], 'is_ptz': None, 'supportedSubResolution': '[{"width": 352, "height": 288}, {"width": 176, "height": 144}]', 'substreamResolution': '352x288', 'supportedSubFps': '[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]', 'substreamFps': '15'}, {'profileToken': 'MediaProfile00300', 'supportedMainResolution': '[{"width": 704, "height": 576}, {"width": 352, "height": 576}, {"width": 704, "height": 288}, {"width": 352, "height": 288}, {"width": 176, "height": 144}, {"width": 1280, "height": 720}, {"width": 960, "height": 576}, {"width": 960, "height": 1080}]', 'mainstreamResolution': '1280x720', 'supportedMainFps': '[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25]', 'mainstreamFps': '25', 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/cam/realmonitor?channel=4&subtype=0&unicast=true&proto=Onvif', 'rtsp://admin:abcd1234@**************:554/cam/realmonitor?channel=4&subtype=1&unicast=true&proto=Onvif'], 'is_ptz': None, 'supportedSubResolution': '[{"width": 352, "height": 288}, {"width": 176, "height": 144}]', 'substreamResolution': '352x288', 'supportedSubFps': '[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]', 'substreamFps': '15'}], 'state': True, 'no': 28, 'total': 138, 'records': [], 'metadata': []}
{'ip': '**************', 'user': 'admin', 'password': 'abcd1234', 'port': 554, 'brand': 'Dahua', 'model': 'DH-IPC-HDWP3158EIZ5E', 'mac_address': 'c0:39:5a:53:6a:27', 'serial_number': '7L03B53PAG2FA93', 'nvr': None, 'number_cameras': 1, 'device_type': 'IPC', 'device_name': 'admin', 'cam_cfg': [{'profileToken': 'MediaProfile00000', 'supportedMainResolution': '[{"width": 3840, "height": 2160}, {"width": 3072, "height": 2048}, {"width": 3072, "height": 1728}, {"width": 2592, "height": 1944}, {"width": 2688, "height": 1520}, {"width": 2304, "height": 1296}, {"width": 1920, "height": 1080}, {"width": 1280, "height": 960}, {"width": 1280, "height": 720}]', 'mainstreamResolution': '3840x2160', 'supportedMainFps': '[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]', 'mainstreamFps': '15', 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/cam/realmonitor?channel=1&subtype=0&unicast=true&proto=Onvif', 'rtsp://admin:abcd1234@**************:554/cam/realmonitor?channel=1&subtype=1&unicast=true&proto=Onvif'], 'is_ptz': True, 'supportedSubResolution': '[{"width": 1920, "height": 1080}, {"width": 1280, "height": 720}, {"width": 704, "height": 576}]', 'substreamResolution': '704x576', 'supportedSubFps': '[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]', 'substreamFps': '15'}], 'state': True, 'no': 29, 'total': 138, 'records': [{'link': 'rtsp://**************:554/cam/realmonitor?channel=1&subtype=0', 'start_time': '2024-11-20T00:00:00Z', 'end_time': '2024-11-20T23:59:59Z', 'record': 'rtsp://admin:abcd1234@**************:554/cam/playback?channel=1&starttime=2024_11_20_00_00_00&endtime=2024_11_20_23_59_59'}], 'metadata': []}
{'ip': '***************', 'user': '888888', 'password': '888888', 'port': 554, 'brand': None, 'model': None, 'mac_address': None, 'serial_number': None, 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [{'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://888888:888888@***************:554/']}], 'state': True, 'no': 30, 'total': 138, 'records': [], 'metadata': []}
{'ip': '**************', 'user': '888888', 'password': '888888', 'port': 554, 'brand': None, 'model': None, 'mac_address': None, 'serial_number': None, 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [{'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://888888:888888@**************:554/']}], 'state': True, 'no': 107, 'total': 138, 'records': [], 'metadata': []}
{'ip': '*************', 'user': 'admin', 'password': 'abcd1234', 'port': 554, 'brand': 'Hikvision', 'model': 'DS-7608NXI-K1', 'mac_address': 'FC:9F:FD:1A:0E:E0', 'serial_number': 'DS-7608NXI-K10820230624CCRRAC8453450WCVU', 'nvr': None, 'number_cameras': 5, 'device_type': '', 'device_name': 'Network Video Recorder', 'cam_cfg': [{'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@*************:554/Streaming/Channels/101/', 'rtsp://admin:abcd1234@*************:554/Streaming/Channels/102/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@*************:554/Streaming/Channels/301/', 'rtsp://admin:abcd1234@*************:554/Streaming/Channels/302/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@*************:554/Streaming/Channels/401/', 'rtsp://admin:abcd1234@*************:554/Streaming/Channels/402/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@*************:554/Streaming/Channels/501/', 'rtsp://admin:abcd1234@*************:554/Streaming/Channels/502/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@*************:554/Streaming/Channels/701/', 'rtsp://admin:abcd1234@*************:554/Streaming/Channels/702/']}], 'state': True, 'no': 108, 'total': 138, 'records': [], 'metadata': []}
{'ip': '**************', 'user': 'admin', 'password': 'abcd1234', 'port': 554, 'brand': None, 'model': None, 'mac_address': None, 'serial_number': None, 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [{'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/']}], 'state': True, 'no': 109, 'total': 138, 'records': [], 'metadata': []}
{'ip': '**************', 'user': 'admin', 'password': '1111', 'port': 554, 'brand': None, 'model': None, 'mac_address': None, 'serial_number': None, 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [{'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:1111@**************:554/']}], 'state': True, 'no': 110, 'total': 138, 'records': [], 'metadata': []}
{'ip': '***************', 'user': 'admin', 'password': 'abcd1234', 'port': 554, 'brand': None, 'model': None, 'mac_address': None, 'serial_number': None, 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [{'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@***************:554/']}], 'state': True, 'no': 111, 'total': 138, 'records': [], 'metadata': []}
{'ip': '***************', 'user': 'admin', 'password': 'abcd1234', 'port': 554, 'brand': 'General', 'model': 'DVR', 'mac_address': None, 'serial_number': None, 'nvr': None, 'number_cameras': 8, 'device_type': '', 'device_name': '', 'cam_cfg': [{'profileToken': 'MediaProfile00000', 'supportedMainResolution': '[{"width": 704, "height": 576}, {"width": 352, "height": 576}, {"width": 704, "height": 288}, {"width": 352, "height": 288}, {"width": 176, "height": 144}, {"width": 1280, "height": 720}, {"width": 1920, "height": 1080}, {"width": 960, "height": 576}, {"width": 960, "height": 1080}]', 'mainstreamResolution': '1920x1080', 'supportedMainFps': '[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25]', 'mainstreamFps': '15', 'rtsp_list': ['rtsp://admin:abcd1234@***************:554/cam/realmonitor?channel=1&subtype=0&unicast=true&proto=Onvif', 'rtsp://admin:abcd1234@***************:554/cam/realmonitor?channel=1&subtype=1&unicast=true&proto=Onvif'], 'is_ptz': None, 'supportedSubResolution': '[{"width": 704, "height": 576}, {"width": 352, "height": 288}, {"width": 176, "height": 144}]', 'substreamResolution': '352x288', 'supportedSubFps': '[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]', 'substreamFps': '15'}, {'profileToken': 'MediaProfile00100', 'supportedMainResolution': '[{"width": 704, "height": 576}, {"width": 352, "height": 576}, {"width": 704, "height": 288}, {"width": 352, "height": 288}, {"width": 176, "height": 144}, {"width": 1280, "height": 720}, {"width": 1920, "height": 1080}, {"width": 960, "height": 576}, {"width": 960, "height": 1080}]', 'mainstreamResolution': '1920x1080', 'supportedMainFps': '[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]', 'mainstreamFps': '15', 'rtsp_list': ['rtsp://admin:abcd1234@***************:554/cam/realmonitor?channel=2&subtype=0&unicast=true&proto=Onvif', 'rtsp://admin:abcd1234@***************:554/cam/realmonitor?channel=2&subtype=1&unicast=true&proto=Onvif'], 'is_ptz': None, 'supportedSubResolution': '[{"width": 704, "height": 576}, {"width": 352, "height": 288}, {"width": 176, "height": 144}]', 'substreamResolution': '352x288', 'supportedSubFps': '[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]', 'substreamFps': '15'}, {'profileToken': 'MediaProfile00200', 'supportedMainResolution': '[{"width": 704, "height": 576}, {"width": 352, "height": 576}, {"width": 704, "height": 288}, {"width": 352, "height": 288}, {"width": 176, "height": 144}, {"width": 1280, "height": 720}, {"width": 1920, "height": 1080}, {"width": 960, "height": 576}, {"width": 960, "height": 1080}]', 'mainstreamResolution': '1920x1080', 'supportedMainFps': '[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]', 'mainstreamFps': '7', 'rtsp_list': ['rtsp://admin:abcd1234@***************:554/cam/realmonitor?channel=3&subtype=0&unicast=true&proto=Onvif', 'rtsp://admin:abcd1234@***************:554/cam/realmonitor?channel=3&subtype=1&unicast=true&proto=Onvif'], 'is_ptz': None, 'supportedSubResolution': '[{"width": 704, "height": 576}, {"width": 352, "height": 288}, {"width": 176, "height": 144}]', 'substreamResolution': '352x288', 'supportedSubFps': '[1, 2, 3, 4, 5, 6, 7]', 'substreamFps': '7'}, {'profileToken': 'MediaProfile00300', 'supportedMainResolution': '[{"width": 704, "height": 576}, {"width": 352, "height": 576}, {"width": 704, "height": 288}, {"width": 352, "height": 288}, {"width": 176, "height": 144}, {"width": 1280, "height": 720}, {"width": 1920, "height": 1080}, {"width": 960, "height": 576}, {"width": 960, "height": 1080}]', 'mainstreamResolution': '1920x1080', 'supportedMainFps': '[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]', 'mainstreamFps': '15', 'rtsp_list': ['rtsp://admin:abcd1234@***************:554/cam/realmonitor?channel=4&subtype=0&unicast=true&proto=Onvif', 'rtsp://admin:abcd1234@***************:554/cam/realmonitor?channel=4&subtype=1&unicast=true&proto=Onvif'], 'is_ptz': None, 'supportedSubResolution': '[{"width": 704, "height": 576}, {"width": 352, "height": 288}, {"width": 176, "height": 144}]', 'substreamResolution': '352x288', 'supportedSubFps': '[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]', 'substreamFps': '15'}, {'profileToken': 'MediaProfile00400', 'supportedMainResolution': '[{"width": 704, "height": 576}, {"width": 352, "height": 576}, {"width": 704, "height": 288}, {"width": 352, "height": 288}, {"width": 176, "height": 144}, {"width": 1280, "height": 720}, {"width": 1920, "height": 1080}, {"width": 960, "height": 576}, {"width": 960, "height": 1080}]', 'mainstreamResolution': '1920x1080', 'supportedMainFps': '[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25]', 'mainstreamFps': '15', 'rtsp_list': ['rtsp://admin:abcd1234@***************:554/cam/realmonitor?channel=5&subtype=0&unicast=true&proto=Onvif', 'rtsp://admin:abcd1234@***************:554/cam/realmonitor?channel=5&subtype=1&unicast=true&proto=Onvif'], 'is_ptz': None, 'supportedSubResolution': '[{"width": 704, "height": 576}, {"width": 352, "height": 288}, {"width": 176, "height": 144}]', 'substreamResolution': '352x288', 'supportedSubFps': '[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]', 'substreamFps': '15'}, {'profileToken': 'MediaProfile00500', 'supportedMainResolution': '[{"width": 704, "height": 576}, {"width": 352, "height": 576}, {"width": 704, "height": 288}, {"width": 352, "height": 288}, {"width": 176, "height": 144}, {"width": 1280, "height": 720}, {"width": 1920, "height": 1080}, {"width": 960, "height": 576}, {"width": 960, "height": 1080}]', 'mainstreamResolution': '1920x1080', 'supportedMainFps': '[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]', 'mainstreamFps': '15', 'rtsp_list': ['rtsp://admin:abcd1234@***************:554/cam/realmonitor?channel=6&subtype=0&unicast=true&proto=Onvif', 'rtsp://admin:abcd1234@***************:554/cam/realmonitor?channel=6&subtype=1&unicast=true&proto=Onvif'], 'is_ptz': None, 'supportedSubResolution': '[{"width": 704, "height": 576}, {"width": 352, "height": 288}, {"width": 176, "height": 144}]', 'substreamResolution': '352x288', 'supportedSubFps': '[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]', 'substreamFps': '15'}, {'profileToken': 'MediaProfile00600', 'supportedMainResolution': '[{"width": 704, "height": 576}, {"width": 352, "height": 576}, {"width": 704, "height": 288}, {"width": 352, "height": 288}, {"width": 176, "height": 144}, {"width": 1280, "height": 720}, {"width": 1920, "height": 1080}, {"width": 960, "height": 576}, {"width": 960, "height": 1080}]', 'mainstreamResolution': '1920x1080', 'supportedMainFps': '[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]', 'mainstreamFps': '15', 'rtsp_list': ['rtsp://admin:abcd1234@***************:554/cam/realmonitor?channel=7&subtype=0&unicast=true&proto=Onvif', 'rtsp://admin:abcd1234@***************:554/cam/realmonitor?channel=7&subtype=1&unicast=true&proto=Onvif'], 'is_ptz': None, 'supportedSubResolution': '[{"width": 704, "height": 576}, {"width": 352, "height": 288}, {"width": 176, "height": 144}]', 'substreamResolution': '352x288', 'supportedSubFps': '[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]', 'substreamFps': '15'}, {'profileToken': 'MediaProfile00700', 'supportedMainResolution': '[{"width": 704, "height": 576}, {"width": 352, "height": 576}, {"width": 704, "height": 288}, {"width": 352, "height": 288}, {"width": 176, "height": 144}, {"width": 1280, "height": 720}, {"width": 1920, "height": 1080}, {"width": 960, "height": 576}, {"width": 960, "height": 1080}]', 'mainstreamResolution': '1920x1080', 'supportedMainFps': '[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]', 'mainstreamFps': '15', 'rtsp_list': ['rtsp://admin:abcd1234@***************:554/cam/realmonitor?channel=8&subtype=0&unicast=true&proto=Onvif', 'rtsp://admin:abcd1234@***************:554/cam/realmonitor?channel=8&subtype=1&unicast=true&proto=Onvif'], 'is_ptz': None, 'supportedSubResolution': '[{"width": 704, "height": 576}, {"width": 352, "height": 288}, {"width": 176, "height": 144}]', 'substreamResolution': '352x288', 'supportedSubFps': '[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]', 'substreamFps': '15'}], 'state': True, 'no': 112, 'total': 138, 'records': [], 'metadata': []}
{'ip': '***************', 'user': 'admin', 'password': 'abcd1234', 'port': 554, 'brand': 'Hikvision', 'model': 'DS-7716NI-K4', 'mac_address': 'AC:B9:2F:26:E5:99', 'serial_number': 'DS-7716NI-K41620211020CCRRG91782671WCVU', 'nvr': None, 'number_cameras': 5, 'device_type': '', 'device_name': 'Network Video Recorder', 'cam_cfg': [{'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@***************:554/Streaming/Channels/101/', 'rtsp://admin:abcd1234@***************:554/Streaming/Channels/102/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@***************:554/Streaming/Channels/201/', 'rtsp://admin:abcd1234@***************:554/Streaming/Channels/202/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@***************:554/Streaming/Channels/401/', 'rtsp://admin:abcd1234@***************:554/Streaming/Channels/402/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@***************:554/Streaming/Channels/601/', 'rtsp://admin:abcd1234@***************:554/Streaming/Channels/602/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@***************:554/Streaming/Channels/701/', 'rtsp://admin:abcd1234@***************:554/Streaming/Channels/702/']}], 'state': True, 'no': 113, 'total': 138, 'records': [], 'metadata': []}
{'ip': '**************', 'user': '', 'password': '', 'port': 554, 'brand': None, 'model': None, 'mac_address': None, 'serial_number': None, 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [{'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://**************:554/']}], 'state': True, 'no': 114, 'total': 138, 'records': [], 'metadata': []}
{'ip': '***************', 'user': 'admin', 'password': 'abcd1234', 'port': 554, 'brand': 'Private', 'model': 'DH-XVR5108HS-X', 'mac_address': None, 'serial_number': None, 'nvr': None, 'number_cameras': 10, 'device_type': '', 'device_name': '', 'cam_cfg': [{'profileToken': 'MediaProfile00000', 'supportedMainResolution': '[{"width": 704, "height": 576}, {"width": 352, "height": 288}, {"width": 1280, "height": 720}, {"width": 1920, "height": 1080}, {"width": 960, "height": 576}, {"width": 960, "height": 1080}, {"width": 1280, "height": 1440}, {"width": 1296, "height": 1944}]', 'mainstreamResolution': '1280x720', 'supportedMainFps': '[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25]', 'mainstreamFps': '25', 'rtsp_list': ['rtsp://admin:abcd1234@***************:554/cam/realmonitor?channel=1&subtype=0&unicast=true&proto=Onvif', 'rtsp://admin:abcd1234@***************:554/cam/realmonitor?channel=1&subtype=1&unicast=true&proto=Onvif'], 'is_ptz': None}, {'profileToken': 'MediaProfile00100', 'rtsp_list': ['rtsp://admin:abcd1234@***************:554/cam/realmonitor?channel=2&subtype=0&unicast=true&proto=Onvif', 'rtsp://admin:abcd1234@***************:554/cam/realmonitor?channel=2&subtype=1&unicast=true&proto=Onvif'], 'is_ptz': None}, {'profileToken': 'MediaProfile00200', 'rtsp_list': ['rtsp://admin:abcd1234@***************:554/cam/realmonitor?channel=3&subtype=0&unicast=true&proto=Onvif', 'rtsp://admin:abcd1234@***************:554/cam/realmonitor?channel=3&subtype=1&unicast=true&proto=Onvif'], 'is_ptz': None}, {'profileToken': 'MediaProfile00300', 'rtsp_list': ['rtsp://admin:abcd1234@***************:554/cam/realmonitor?channel=4&subtype=0&unicast=true&proto=Onvif', 'rtsp://admin:abcd1234@***************:554/cam/realmonitor?channel=4&subtype=1&unicast=true&proto=Onvif'], 'is_ptz': None}, {'profileToken': 'MediaProfile00400', 'rtsp_list': ['rtsp://admin:abcd1234@***************:554/cam/realmonitor?channel=5&subtype=0&unicast=true&proto=Onvif', 'rtsp://admin:abcd1234@***************:554/cam/realmonitor?channel=5&subtype=1&unicast=true&proto=Onvif'], 'is_ptz': None}, {'profileToken': 'MediaProfile00500', 'rtsp_list': ['rtsp://admin:abcd1234@***************:554/cam/realmonitor?channel=6&subtype=0&unicast=true&proto=Onvif', 'rtsp://admin:abcd1234@***************:554/cam/realmonitor?channel=6&subtype=1&unicast=true&proto=Onvif'], 'is_ptz': None}, {'profileToken': 'MediaProfile00600', 'rtsp_list': ['rtsp://admin:abcd1234@***************:554/cam/realmonitor?channel=7&subtype=0&unicast=true&proto=Onvif', 'rtsp://admin:abcd1234@***************:554/cam/realmonitor?channel=7&subtype=1&unicast=true&proto=Onvif'], 'is_ptz': None}, {'profileToken': 'MediaProfile00700', 'rtsp_list': ['rtsp://admin:abcd1234@***************:554/cam/realmonitor?channel=8&subtype=0&unicast=true&proto=Onvif', 'rtsp://admin:abcd1234@***************:554/cam/realmonitor?channel=8&subtype=1&unicast=true&proto=Onvif'], 'is_ptz': None}, {'profileToken': 'MediaProfile00800', 'supportedMainResolution': '[{"width": 1280, "height": 720}, {"width": 1920, "height": 1080}, {"width": 1280, "height": 960}, {"width": 2560, "height": 1440}, {"width": 2304, "height": 1296}]', 'mainstreamResolution': '1920x1080', 'supportedMainFps': '[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25]', 'mainstreamFps': '25', 'rtsp_list': ['rtsp://admin:abcd1234@***************:554/cam/realmonitor?channel=9&subtype=0&unicast=true&proto=Onvif', 'rtsp://admin:abcd1234@***************:554/cam/realmonitor?channel=9&subtype=1&unicast=true&proto=Onvif'], 'is_ptz': None, 'supportedSubResolution': '[{"width": 704, "height": 576}, {"width": 352, "height": 288}, {"width": 640, "height": 480}]', 'substreamResolution': '704x576', 'supportedSubFps': '[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25]', 'substreamFps': '25'}, {'profileToken': 'MediaProfile01000', 'supportedMainResolution': '[{"width": 1280, "height": 720}, {"width": 1920, "height": 1080}, {"width": 1280, "height": 960}, {"width": 2560, "height": 1440}, {"width": 2304, "height": 1296}]', 'mainstreamResolution': '1920x1080', 'supportedMainFps': '[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25]', 'mainstreamFps': '25', 'rtsp_list': ['rtsp://admin:abcd1234@***************:554/cam/realmonitor?channel=11&subtype=0&unicast=true&proto=Onvif', 'rtsp://admin:abcd1234@***************:554/cam/realmonitor?channel=11&subtype=1&unicast=true&proto=Onvif'], 'is_ptz': None, 'supportedSubResolution': '[{"width": 704, "height": 576}, {"width": 352, "height": 288}, {"width": 640, "height": 480}]', 'substreamResolution': '704x576', 'supportedSubFps': '[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25]', 'substreamFps': '25'}], 'state': True, 'no': 115, 'total': 138, 'records': [], 'metadata': []}
{'ip': '***************', 'user': 'admin', 'password': 'abcd1234', 'port': 554, 'brand': None, 'model': None, 'mac_address': None, 'serial_number': None, 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [{'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@***************:554/']}], 'state': True, 'no': 116, 'total': 138, 'records': [], 'metadata': []}
{'ip': '**********', 'user': 'admin', 'password': 'abcd1234', 'port': 554, 'brand': 'Hikvision', 'model': 'DS-7104NI-Q1/M', 'mac_address': '80:7C:62:2E:45:7B', 'serial_number': 'DS-7104NI-Q1/M0420220524CCRRK01364193WVU', 'nvr': None, 'number_cameras': 4, 'device_type': '', 'device_name': 'Victory', 'cam_cfg': [{'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@**********:554/Streaming/Channels/101/', 'rtsp://admin:abcd1234@**********:554/Streaming/Channels/102/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@**********:554/Streaming/Channels/201/', 'rtsp://admin:abcd1234@**********:554/Streaming/Channels/202/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@**********:554/Streaming/Channels/301/', 'rtsp://admin:abcd1234@**********:554/Streaming/Channels/302/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@**********:554/Streaming/Channels/401/', 'rtsp://admin:abcd1234@**********:554/Streaming/Channels/402/']}], 'state': True, 'no': 117, 'total': 138, 'records': [], 'metadata': []}
{'ip': '***************', 'user': '888888', 'password': '888888', 'port': 554, 'brand': None, 'model': None, 'mac_address': None, 'serial_number': None, 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [{'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://888888:888888@***************:554/']}], 'state': True, 'no': 118, 'total': 138, 'records': [], 'metadata': []}
{'ip': '***************', 'user': 'admin', 'password': 'abcd1234', 'port': 554, 'brand': None, 'model': None, 'mac_address': None, 'serial_number': None, 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [{'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@***************:554/']}], 'state': True, 'no': 119, 'total': 138, 'records': [], 'metadata': []}
{'ip': '************', 'user': 'admin', 'password': 'hd543211', 'port': 554, 'brand': 'Hikvision', 'model': 'HDS-7208QTVI-HDMI', 'mac_address': '28:57:BE:A9:D5:DB', 'serial_number': 'HDS-7208QTVI-HDMI0820160122AAWR570777334WCVU', 'nvr': None, 'number_cameras': 8, 'device_type': '', 'device_name': 'Embedded Net DVR', 'cam_cfg': [{'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:hd543211@************:554/Streaming/Channels/101/', 'rtsp://admin:hd543211@************:554/Streaming/Channels/102/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:hd543211@************:554/Streaming/Channels/201/', 'rtsp://admin:hd543211@************:554/Streaming/Channels/202/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:hd543211@************:554/Streaming/Channels/301/', 'rtsp://admin:hd543211@************:554/Streaming/Channels/302/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:hd543211@************:554/Streaming/Channels/401/', 'rtsp://admin:hd543211@************:554/Streaming/Channels/402/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:hd543211@************:554/Streaming/Channels/501/', 'rtsp://admin:hd543211@************:554/Streaming/Channels/502/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:hd543211@************:554/Streaming/Channels/601/', 'rtsp://admin:hd543211@************:554/Streaming/Channels/602/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:hd543211@************:554/Streaming/Channels/701/', 'rtsp://admin:hd543211@************:554/Streaming/Channels/702/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:hd543211@************:554/Streaming/Channels/801/', 'rtsp://admin:hd543211@************:554/Streaming/Channels/802/']}], 'state': True, 'no': 120, 'total': 138, 'records': [], 'metadata': []}
{'ip': '***************', 'user': '', 'password': '', 'port': 554, 'brand': None, 'model': None, 'mac_address': None, 'serial_number': None, 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [{'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://***************:554/']}], 'state': True, 'no': 121, 'total': 138, 'records': [], 'metadata': []}
{'ip': '************', 'user': '888888', 'password': '888888', 'port': 554, 'brand': None, 'model': None, 'mac_address': None, 'serial_number': None, 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [{'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://888888:888888@************:554/']}], 'state': True, 'no': 122, 'total': 138, 'records': [], 'metadata': []}
{'ip': '***************', 'user': 'admin', 'password': 'abcd1234', 'port': 554, 'brand': 'Hikvision', 'model': 'DS-7616NI-K2', 'mac_address': '24:28:FD:17:42:04', 'serial_number': 'DS-7616NI-K21620210701CCRRG28009881WCVU', 'nvr': None, 'number_cameras': 11, 'device_type': '', 'device_name': 'Network Video Recorder', 'cam_cfg': [{'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@***************:554/Streaming/Channels/101/', 'rtsp://admin:abcd1234@***************:554/Streaming/Channels/102/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@***************:554/Streaming/Channels/201/', 'rtsp://admin:abcd1234@***************:554/Streaming/Channels/202/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@***************:554/Streaming/Channels/301/', 'rtsp://admin:abcd1234@***************:554/Streaming/Channels/302/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@***************:554/Streaming/Channels/401/', 'rtsp://admin:abcd1234@***************:554/Streaming/Channels/402/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@***************:554/Streaming/Channels/501/', 'rtsp://admin:abcd1234@***************:554/Streaming/Channels/502/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@***************:554/Streaming/Channels/601/', 'rtsp://admin:abcd1234@***************:554/Streaming/Channels/602/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@***************:554/Streaming/Channels/701/', 'rtsp://admin:abcd1234@***************:554/Streaming/Channels/702/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@***************:554/Streaming/Channels/801/', 'rtsp://admin:abcd1234@***************:554/Streaming/Channels/802/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@***************:554/Streaming/Channels/901/', 'rtsp://admin:abcd1234@***************:554/Streaming/Channels/902/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@***************:554/Streaming/Channels/1001/', 'rtsp://admin:abcd1234@***************:554/Streaming/Channels/1002/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@***************:554/Streaming/Channels/1201/', 'rtsp://admin:abcd1234@***************:554/Streaming/Channels/1202/']}], 'state': True, 'no': 123, 'total': 138, 'records': [], 'metadata': []}
{'ip': '**************', 'user': 'admin', 'password': 'abcd1234', 'port': 554, 'brand': 'Hikvision', 'model': 'DS-9664NI-I8', 'mac_address': '24:0F:9B:33:C7:49', 'serial_number': 'DS-9664NI-I81620220302CCRRJ56750343WCVU', 'nvr': None, 'number_cameras': 13, 'device_type': '', 'device_name': 'Network Video Recorder', 'cam_cfg': [{'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/Streaming/Channels/201/', 'rtsp://admin:abcd1234@**************:554/Streaming/Channels/202/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/Streaming/Channels/301/', 'rtsp://admin:abcd1234@**************:554/Streaming/Channels/302/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/Streaming/Channels/401/', 'rtsp://admin:abcd1234@**************:554/Streaming/Channels/402/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/Streaming/Channels/501/', 'rtsp://admin:abcd1234@**************:554/Streaming/Channels/502/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/Streaming/Channels/701/', 'rtsp://admin:abcd1234@**************:554/Streaming/Channels/702/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/Streaming/Channels/801/', 'rtsp://admin:abcd1234@**************:554/Streaming/Channels/802/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/Streaming/Channels/901/', 'rtsp://admin:abcd1234@**************:554/Streaming/Channels/902/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/Streaming/Channels/1001/', 'rtsp://admin:abcd1234@**************:554/Streaming/Channels/1002/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/Streaming/Channels/1101/', 'rtsp://admin:abcd1234@**************:554/Streaming/Channels/1102/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/Streaming/Channels/1201/', 'rtsp://admin:abcd1234@**************:554/Streaming/Channels/1202/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/Streaming/Channels/1301/', 'rtsp://admin:abcd1234@**************:554/Streaming/Channels/1302/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/Streaming/Channels/1401/', 'rtsp://admin:abcd1234@**************:554/Streaming/Channels/1402/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/Streaming/Channels/1501/', 'rtsp://admin:abcd1234@**************:554/Streaming/Channels/1502/']}], 'state': True, 'no': 124, 'total': 138, 'records': [], 'metadata': []}
{'ip': '**************', 'user': 'admin', 'password': 'abcd1234', 'port': 554, 'brand': 'Hikvision', 'model': 'DS-7616NI-K1(B)', 'mac_address': '84:9A:40:6D:61:95', 'serial_number': 'DS-7616NI-K1(B)1620191119CCRRD88452949WCVU', 'nvr': None, 'number_cameras': 10, 'device_type': '', 'device_name': 'Network Video Recorder', 'cam_cfg': [{'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/Streaming/Channels/101/', 'rtsp://admin:abcd1234@**************:554/Streaming/Channels/102/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/Streaming/Channels/201/', 'rtsp://admin:abcd1234@**************:554/Streaming/Channels/202/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/Streaming/Channels/401/', 'rtsp://admin:abcd1234@**************:554/Streaming/Channels/402/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/Streaming/Channels/501/', 'rtsp://admin:abcd1234@**************:554/Streaming/Channels/502/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/Streaming/Channels/601/', 'rtsp://admin:abcd1234@**************:554/Streaming/Channels/602/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/Streaming/Channels/701/', 'rtsp://admin:abcd1234@**************:554/Streaming/Channels/702/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/Streaming/Channels/801/', 'rtsp://admin:abcd1234@**************:554/Streaming/Channels/802/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/Streaming/Channels/1001/', 'rtsp://admin:abcd1234@**************:554/Streaming/Channels/1002/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/Streaming/Channels/1101/', 'rtsp://admin:abcd1234@**************:554/Streaming/Channels/1102/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/Streaming/Channels/1201/', 'rtsp://admin:abcd1234@**************:554/Streaming/Channels/1202/']}], 'state': True, 'no': 125, 'total': 138, 'records': [], 'metadata': []}
{'ip': '***************', 'user': '', 'password': '', 'port': 554, 'brand': None, 'model': None, 'mac_address': None, 'serial_number': None, 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [{'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://***************:554/']}], 'state': True, 'no': 126, 'total': 138, 'records': [], 'metadata': []}
{'ip': '**************', 'user': 'admin', 'password': 'abcd1234', 'port': 554, 'brand': None, 'model': None, 'mac_address': None, 'serial_number': None, 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [{'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/']}], 'state': True, 'no': 127, 'total': 138, 'records': [], 'metadata': []}
{'ip': '***************', 'user': 'admin', 'password': 'abcd1234', 'port': 554, 'brand': None, 'model': None, 'mac_address': None, 'serial_number': None, 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [{'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@***************:554/']}], 'state': True, 'no': 128, 'total': 138, 'records': [], 'metadata': []}
{'ip': '***************', 'user': 'admin', 'password': 'hd543211', 'port': 554, 'brand': None, 'model': None, 'mac_address': None, 'serial_number': None, 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [{'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:hd543211@***************:554/']}], 'state': True, 'no': 129, 'total': 138, 'records': [], 'metadata': []}
{'ip': '***************', 'user': 'admin', 'password': 'abcd1234', 'port': 554, 'brand': None, 'model': None, 'mac_address': None, 'serial_number': None, 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [{'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@***************:554/']}], 'state': True, 'no': 130, 'total': 138, 'records': [], 'metadata': []}
{'ip': '***************', 'user': 'admin', 'password': 'abcd1234', 'port': 554, 'brand': None, 'model': None, 'mac_address': None, 'serial_number': None, 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [{'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@***************:554/']}], 'state': True, 'no': 131, 'total': 138, 'records': [], 'metadata': []}
{'ip': '***************', 'user': 'admin', 'password': 'hd543211', 'port': 554, 'brand': None, 'model': None, 'mac_address': None, 'serial_number': None, 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [{'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:hd543211@***************:554/']}], 'state': True, 'no': 132, 'total': 138, 'records': [], 'metadata': []}
{'ip': '**************', 'user': 'admin', 'password': 'admin123', 'port': 554, 'brand': None, 'model': None, 'mac_address': None, 'serial_number': None, 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [{'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:admin123@**************:554/']}], 'state': True, 'no': 133, 'total': 138, 'records': [], 'metadata': []}
{'ip': '************', 'user': 'admin', 'password': 'admin123', 'port': 554, 'brand': None, 'model': None, 'mac_address': None, 'serial_number': None, 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [{'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:admin123@************:554/']}], 'state': True, 'no': 134, 'total': 138, 'records': [], 'metadata': []}
{'ip': '***************', 'user': 'admin', 'password': 'abcd1234', 'port': 554, 'brand': 'Hikvision', 'model': 'DS-7108HGHI-F1/N', 'mac_address': '98:DF:82:C4:CD:ED', 'serial_number': 'DS-7108HGHI-F1/N0820200301CCWRE18031772WCVU', 'nvr': None, 'number_cameras': 10, 'device_type': '', 'device_name': 'Embedded Net DVR', 'cam_cfg': [{'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@***************:554/Streaming/Channels/101/', 'rtsp://admin:abcd1234@***************:554/Streaming/Channels/102/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@***************:554/Streaming/Channels/201/', 'rtsp://admin:abcd1234@***************:554/Streaming/Channels/202/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@***************:554/Streaming/Channels/301/', 'rtsp://admin:abcd1234@***************:554/Streaming/Channels/302/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@***************:554/Streaming/Channels/401/', 'rtsp://admin:abcd1234@***************:554/Streaming/Channels/402/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@***************:554/Streaming/Channels/501/', 'rtsp://admin:abcd1234@***************:554/Streaming/Channels/502/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@***************:554/Streaming/Channels/601/', 'rtsp://admin:abcd1234@***************:554/Streaming/Channels/602/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@***************:554/Streaming/Channels/701/', 'rtsp://admin:abcd1234@***************:554/Streaming/Channels/702/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@***************:554/Streaming/Channels/801/', 'rtsp://admin:abcd1234@***************:554/Streaming/Channels/802/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@***************:554/Streaming/Channels/901/', 'rtsp://admin:abcd1234@***************:554/Streaming/Channels/902/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@***************:554/Streaming/Channels/1001/', 'rtsp://admin:abcd1234@***************:554/Streaming/Channels/1002/']}], 'state': True, 'no': 135, 'total': 138, 'records': [], 'metadata': []}
{'ip': '***************', 'user': 'admin', 'password': 'abcd1234', 'port': 554, 'brand': 'Hikvision', 'model': 'DS-7216HGHI-E1', 'mac_address': '28:57:BE:EE:D7:1F', 'serial_number': 'DS-7216HGHI-E11620160331AAWR586063742WCVU', 'nvr': None, 'number_cameras': 16, 'device_type': '', 'device_name': 'Victory', 'cam_cfg': [{'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@***************:554/Streaming/Channels/101/', 'rtsp://admin:abcd1234@***************:554/Streaming/Channels/102/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@***************:554/Streaming/Channels/201/', 'rtsp://admin:abcd1234@***************:554/Streaming/Channels/202/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@***************:554/Streaming/Channels/301/', 'rtsp://admin:abcd1234@***************:554/Streaming/Channels/302/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@***************:554/Streaming/Channels/401/', 'rtsp://admin:abcd1234@***************:554/Streaming/Channels/402/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@***************:554/Streaming/Channels/501/', 'rtsp://admin:abcd1234@***************:554/Streaming/Channels/502/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@***************:554/Streaming/Channels/601/', 'rtsp://admin:abcd1234@***************:554/Streaming/Channels/602/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@***************:554/Streaming/Channels/701/', 'rtsp://admin:abcd1234@***************:554/Streaming/Channels/702/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@***************:554/Streaming/Channels/801/', 'rtsp://admin:abcd1234@***************:554/Streaming/Channels/802/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@***************:554/Streaming/Channels/901/', 'rtsp://admin:abcd1234@***************:554/Streaming/Channels/902/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@***************:554/Streaming/Channels/1001/', 'rtsp://admin:abcd1234@***************:554/Streaming/Channels/1002/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@***************:554/Streaming/Channels/1101/', 'rtsp://admin:abcd1234@***************:554/Streaming/Channels/1102/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@***************:554/Streaming/Channels/1201/', 'rtsp://admin:abcd1234@***************:554/Streaming/Channels/1202/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@***************:554/Streaming/Channels/1301/', 'rtsp://admin:abcd1234@***************:554/Streaming/Channels/1302/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@***************:554/Streaming/Channels/1401/', 'rtsp://admin:abcd1234@***************:554/Streaming/Channels/1402/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@***************:554/Streaming/Channels/1501/', 'rtsp://admin:abcd1234@***************:554/Streaming/Channels/1502/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@***************:554/Streaming/Channels/1601/', 'rtsp://admin:abcd1234@***************:554/Streaming/Channels/1602/']}], 'state': True, 'no': 136, 'total': 138, 'records': [], 'metadata': []}
{'ip': '**************', 'user': 'admin', 'password': 'abcd1234', 'port': 554, 'brand': 'Hikvision', 'model': 'DS-7108HGHI-SH', 'mac_address': '28:57:BE:A2:CF:DC', 'serial_number': 'DS-7108HGHI-SH0820160105AAWR567494147WCVU', 'nvr': None, 'number_cameras': 8, 'device_type': '', 'device_name': 'Embedded Net DVR', 'cam_cfg': [{'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/Streaming/Channels/101/', 'rtsp://admin:abcd1234@**************:554/Streaming/Channels/102/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/Streaming/Channels/201/', 'rtsp://admin:abcd1234@**************:554/Streaming/Channels/202/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/Streaming/Channels/301/', 'rtsp://admin:abcd1234@**************:554/Streaming/Channels/302/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/Streaming/Channels/401/', 'rtsp://admin:abcd1234@**************:554/Streaming/Channels/402/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/Streaming/Channels/501/', 'rtsp://admin:abcd1234@**************:554/Streaming/Channels/502/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/Streaming/Channels/601/', 'rtsp://admin:abcd1234@**************:554/Streaming/Channels/602/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/Streaming/Channels/701/', 'rtsp://admin:abcd1234@**************:554/Streaming/Channels/702/']}, {'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://admin:abcd1234@**************:554/Streaming/Channels/801/', 'rtsp://admin:abcd1234@**************:554/Streaming/Channels/802/']}], 'state': True, 'no': 137, 'total': 138, 'records': [], 'metadata': []}
