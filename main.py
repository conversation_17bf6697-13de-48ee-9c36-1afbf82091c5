from cam_scan.cam_scan import CamScan
import logging
from pathlib import Path
import os
import time

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Paths
FOLDER = Path.cwd()
RESULT_FILE = FOLDER / "result.txt"
INPUT_FILE = FOLDER / "all_vn.txt"
IP_LIST_DEFAULT = ["***************"]

# Global variable to hold the result file
result_file = None


def clear_file(file_path):
    """
    Clears the contents of a file if it exists.
    """
    if os.path.exists(file_path):
        with open(file_path, 'w') as file:
            file.truncate(0)


def callback_result(data):
    """
    Callback to handle scan results.
    Logs and writes results to the already open file.
    """
    global result_file
    if data.get('state'):
        logger.info(f"Callback result: {data}")
        if result_file:
            result_file.write(f"{data}\n")


def initialize_scan(options):
    """
    Initializes and runs the camera scanning process.
    """
    start_time = time.time()

    # Open the result file once and store it in the global variable
    global result_file
    with RESULT_FILE.open("a") as result_file:
        cam_scan = CamScan(options=options)
        cam_scan.scan()  # Assuming the scan method internally calls the callback

    logger.info(f"Scan completed in {time.time() - start_time:.2f} seconds.")


def main():
    """
    Main function to set up and run the scan.
    """
    clear_file(RESULT_FILE)

    options = {
        "callback_result": callback_result,
        "file_path": INPUT_FILE,
    }

    initialize_scan(options)


if __name__ == "__main__":
    main()
