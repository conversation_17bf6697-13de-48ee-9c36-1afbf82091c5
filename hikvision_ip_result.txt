{'ip': '*************', 'user': None, 'password': None, 'port': 554, 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 1, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************', 'user': None, 'password': None, 'port': 554, 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 2, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************', 'user': None, 'password': None, 'port': 554, 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 3, 'total': 256, 'records': [], 'metadata': []}
{'ip': '***********', 'user': None, 'password': None, 'port': 554, 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_credentials', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 4, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 5, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 6, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 7, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 8, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 9, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 10, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 11, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 12, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 13, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 14, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 15, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 16, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 17, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 18, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 19, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 20, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 21, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 22, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 23, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 24, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 25, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 26, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 27, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 28, 'total': 256, 'records': [], 'metadata': []}
{'ip': '***********8', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 29, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 30, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 31, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 32, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************3', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 33, 'total': 256, 'records': [], 'metadata': []}
{'ip': '***********', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 34, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 35, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 36, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 37, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************7', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 38, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************4', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 39, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 40, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 41, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 42, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 43, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 44, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************0', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 45, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 46, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 47, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 48, 'total': 256, 'records': [], 'metadata': []}
{'ip': '***********', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 49, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************5', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 50, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 51, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 52, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 53, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 54, 'total': 256, 'records': [], 'metadata': []}
{'ip': '***********1', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 55, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 56, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 57, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 58, 'total': 256, 'records': [], 'metadata': []}
{'ip': '***********7', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 59, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 60, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************5', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 61, 'total': 256, 'records': [], 'metadata': []}
{'ip': '***********4', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 62, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 63, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 64, 'total': 256, 'records': [], 'metadata': []}
{'ip': '***********8', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 65, 'total': 256, 'records': [], 'metadata': []}
{'ip': '***********3', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 66, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************1', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 67, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 68, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 69, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 70, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 71, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 72, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 73, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 74, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 75, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 76, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 77, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 78, 'total': 256, 'records': [], 'metadata': []}
{'ip': '***********1', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 79, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 80, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 81, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 82, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 83, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************5', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 84, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 85, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 86, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 87, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 88, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 89, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************8', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 90, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************9', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 91, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************9', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 92, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 93, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 94, 'total': 256, 'records': [], 'metadata': []}
{'ip': '***********2', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 95, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 96, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 97, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 98, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************3', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 99, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 100, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 101, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 102, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************3', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 103, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************0', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 104, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 105, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 106, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 107, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 108, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 109, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 110, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 111, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************0', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 112, 'total': 256, 'records': [], 'metadata': []}
{'ip': '***********1', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 113, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 114, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 115, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 116, 'total': 256, 'records': [], 'metadata': []}
{'ip': '***********9', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 117, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 118, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************2', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 119, 'total': 256, 'records': [], 'metadata': []}
{'ip': '***********5', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 120, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 121, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 122, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 123, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************0', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 124, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 125, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************1', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 126, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 127, 'total': 256, 'records': [], 'metadata': []}
{'ip': '***********', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 128, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 129, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************5', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 130, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 131, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************6', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 132, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 133, 'total': 256, 'records': [], 'metadata': []}
{'ip': '***********4', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 134, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************1', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 135, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 136, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 137, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 138, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 139, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************4', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 140, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 141, 'total': 256, 'records': [], 'metadata': []}
{'ip': '***********6', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 142, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 143, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 144, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 145, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 146, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 147, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************5', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 148, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 149, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 150, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 151, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 152, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 153, 'total': 256, 'records': [], 'metadata': []}
{'ip': '***********0', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 154, 'total': 256, 'records': [], 'metadata': []}
{'ip': '***********', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 155, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 156, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 157, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 158, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 159, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************5', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 160, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************1', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 161, 'total': 256, 'records': [], 'metadata': []}
{'ip': '***********4', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 162, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************0', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 163, 'total': 256, 'records': [], 'metadata': []}
{'ip': '***********8', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 164, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 165, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 166, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 167, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 168, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************6', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 169, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 170, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 171, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 172, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************1', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 173, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 174, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 175, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************6', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 176, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************1', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 177, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 178, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 179, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 180, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************7', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 181, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 182, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************8', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 183, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************3', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 184, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 185, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************5', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 186, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 187, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************5', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 188, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************7', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 189, 'total': 256, 'records': [], 'metadata': []}
{'ip': '***********', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 190, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 191, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 192, 'total': 256, 'records': [], 'metadata': []}
{'ip': '***********', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 193, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************0', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 194, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************9', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 195, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************2', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 196, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 197, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 198, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************4', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 199, 'total': 256, 'records': [], 'metadata': []}
{'ip': '***********8', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 200, 'total': 256, 'records': [], 'metadata': []}
{'ip': '***********5', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 201, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 202, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 203, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************8', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 204, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************8', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 205, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************6', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 206, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 207, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 208, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************0', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 209, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************1', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 210, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************0', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 211, 'total': 256, 'records': [], 'metadata': []}
{'ip': '***********7', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 212, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************7', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 213, 'total': 256, 'records': [], 'metadata': []}
{'ip': '***********9', 'user': None, 'password': None, 'port': '', 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_routes', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 214, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************5', 'user': 'admin', 'password': 'abcd1234', 'port': 554, 'brand': 'Dahua', 'model': 'DH-IPC-HDWP3158EIZ5E', 'mac_address': 'f4:b1:c2:b0:36:40', 'serial_number': 'brute_onvif', 'nvr': None, 'number_cameras': 1, 'device_type': 'IPC', 'device_name': '9F079F4PAGC6088', 'cam_cfg': [{'profileToken': 'MediaProfile00000', 'supportedMainResolution': '[{"width": 3840, "height": 2160}, {"width": 3072, "height": 2048}, {"width": 3072, "height": 1728}, {"width": 2592, "height": 1944}, {"width": 2688, "height": 1520}, {"width": 2304, "height": 1296}, {"width": 1920, "height": 1080}, {"width": 1280, "height": 960}, {"width": 1280, "height": 720}]', 'mainstreamResolution': '3840x2160', 'supportedMainFps': '[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]', 'mainstreamFps': '15', 'rtsp_list': ['rtsp://admin:abcd1234@************5:554/cam/realmonitor?channel=1&subtype=0&unicast=true&proto=Onvif', 'rtsp://admin:abcd1234@************5:554/cam/realmonitor?channel=1&subtype=1&unicast=true&proto=Onvif'], 'is_ptz': True, 'supportedSubResolution': '[{"width": 1920, "height": 1080}, {"width": 1280, "height": 720}, {"width": 704, "height": 576}]', 'substreamResolution': '704x576', 'supportedSubFps': '[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]', 'substreamFps': '15'}], 'state': True, 'no': 215, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************2', 'user': 'admin', 'password': 'abcd1234', 'port': 554, 'brand': 'Dahua', 'model': 'DH-IPC-HDWP3158EIZ5E', 'mac_address': 'f4:b1:c2:b0:36:47', 'serial_number': 'brute_onvif', 'nvr': None, 'number_cameras': 1, 'device_type': 'IPC', 'device_name': '9F079F4PAGD13C5', 'cam_cfg': [{'profileToken': 'MediaProfile00000', 'supportedMainResolution': '[{"width": 3840, "height": 2160}, {"width": 3072, "height": 2048}, {"width": 3072, "height": 1728}, {"width": 2592, "height": 1944}, {"width": 2688, "height": 1520}, {"width": 2304, "height": 1296}, {"width": 1920, "height": 1080}, {"width": 1280, "height": 960}, {"width": 1280, "height": 720}]', 'mainstreamResolution': '3840x2160', 'supportedMainFps': '[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]', 'mainstreamFps': '15', 'rtsp_list': ['rtsp://admin:abcd1234@************2:554/cam/realmonitor?channel=1&subtype=0&unicast=true&proto=Onvif', 'rtsp://admin:abcd1234@************2:554/cam/realmonitor?channel=1&subtype=1&unicast=true&proto=Onvif'], 'is_ptz': True, 'supportedSubResolution': '[{"width": 1920, "height": 1080}, {"width": 1280, "height": 720}, {"width": 704, "height": 576}]', 'substreamResolution': '704x576', 'supportedSubFps': '[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]', 'substreamFps': '15'}], 'state': True, 'no': 216, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************', 'user': 'admin', 'password': 'abcd1234', 'port': 554, 'brand': 'Dahua', 'model': 'DH-IPC-HDWP3158EIZ5E', 'mac_address': 'f4:b1:c2:b0:36:3c', 'serial_number': 'brute_onvif', 'nvr': None, 'number_cameras': 1, 'device_type': 'IPC', 'device_name': '9F079F4PAG2A5E6', 'cam_cfg': [{'profileToken': 'MediaProfile00000', 'supportedMainResolution': '[{"width": 3840, "height": 2160}, {"width": 3072, "height": 2048}, {"width": 3072, "height": 1728}, {"width": 2592, "height": 1944}, {"width": 2688, "height": 1520}, {"width": 2304, "height": 1296}, {"width": 1920, "height": 1080}, {"width": 1280, "height": 960}, {"width": 1280, "height": 720}]', 'mainstreamResolution': '3840x2160', 'supportedMainFps': '[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]', 'mainstreamFps': '15', 'rtsp_list': ['rtsp://admin:abcd1234@************:554/cam/realmonitor?channel=1&subtype=0&unicast=true&proto=Onvif', 'rtsp://admin:abcd1234@************:554/cam/realmonitor?channel=1&subtype=1&unicast=true&proto=Onvif'], 'is_ptz': True, 'supportedSubResolution': '[{"width": 1920, "height": 1080}, {"width": 1280, "height": 720}, {"width": 704, "height": 576}]', 'substreamResolution': '704x576', 'supportedSubFps': '[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]', 'substreamFps': '15'}], 'state': True, 'no': 217, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': 'admin', 'password': 'abcd1234', 'port': 554, 'brand': 'Dahua', 'model': 'DH-IPC-HDWP3158EIZ5E', 'mac_address': 'f4:b1:c2:b0:36:38', 'serial_number': 'brute_onvif', 'nvr': None, 'number_cameras': 1, 'device_type': 'IPC', 'device_name': '9F079F4PAG7E2C3', 'cam_cfg': [{'profileToken': 'MediaProfile00000', 'supportedMainResolution': '[{"width": 3840, "height": 2160}, {"width": 3072, "height": 2048}, {"width": 3072, "height": 1728}, {"width": 2592, "height": 1944}, {"width": 2688, "height": 1520}, {"width": 2304, "height": 1296}, {"width": 1920, "height": 1080}, {"width": 1280, "height": 960}, {"width": 1280, "height": 720}]', 'mainstreamResolution': '3840x2160', 'supportedMainFps': '[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]', 'mainstreamFps': '15', 'rtsp_list': ['rtsp://admin:abcd1234@*************:554/cam/realmonitor?channel=1&subtype=0&unicast=true&proto=Onvif', 'rtsp://admin:abcd1234@*************:554/cam/realmonitor?channel=1&subtype=1&unicast=true&proto=Onvif'], 'is_ptz': True, 'supportedSubResolution': '[{"width": 1920, "height": 1080}, {"width": 1280, "height": 720}, {"width": 704, "height": 576}]', 'substreamResolution': '704x576', 'supportedSubFps': '[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]', 'substreamFps': '15'}], 'state': True, 'no': 218, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************2', 'user': 'admin', 'password': 'abcd1234', 'port': 554, 'brand': 'Dahua', 'model': 'DH-IPC-HDWP3158EIZ5E', 'mac_address': 'f4:b1:c2:b0:36:3d', 'serial_number': 'brute_onvif', 'nvr': None, 'number_cameras': 1, 'device_type': 'IPC', 'device_name': '9F079F4PAG72402', 'cam_cfg': [{'profileToken': 'MediaProfile00000', 'supportedMainResolution': '[{"width": 3840, "height": 2160}, {"width": 3072, "height": 2048}, {"width": 3072, "height": 1728}, {"width": 2592, "height": 1944}, {"width": 2688, "height": 1520}, {"width": 2304, "height": 1296}, {"width": 1920, "height": 1080}, {"width": 1280, "height": 960}, {"width": 1280, "height": 720}]', 'mainstreamResolution': '3840x2160', 'supportedMainFps': '[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]', 'mainstreamFps': '15', 'rtsp_list': ['rtsp://admin:abcd1234@************2:554/cam/realmonitor?channel=1&subtype=0&unicast=true&proto=Onvif', 'rtsp://admin:abcd1234@************2:554/cam/realmonitor?channel=1&subtype=1&unicast=true&proto=Onvif'], 'is_ptz': True, 'supportedSubResolution': '[{"width": 1920, "height": 1080}, {"width": 1280, "height": 720}, {"width": 704, "height": 576}]', 'substreamResolution': '704x576', 'supportedSubFps': '[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]', 'substreamFps': '15'}], 'state': True, 'no': 219, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************', 'user': 'admin', 'password': 'abcd1234', 'port': 554, 'brand': 'Dahua', 'model': 'DH-IPC-HDWP3158EIZ5E', 'mac_address': 'f4:b1:c2:b0:36:35', 'serial_number': 'brute_onvif', 'nvr': None, 'number_cameras': 1, 'device_type': 'IPC', 'device_name': '9F079F4PAGB129A', 'cam_cfg': [{'profileToken': 'MediaProfile00000', 'supportedMainResolution': '[{"width": 3840, "height": 2160}, {"width": 3072, "height": 2048}, {"width": 3072, "height": 1728}, {"width": 2592, "height": 1944}, {"width": 2688, "height": 1520}, {"width": 2304, "height": 1296}, {"width": 1920, "height": 1080}, {"width": 1280, "height": 960}, {"width": 1280, "height": 720}]', 'mainstreamResolution': '3840x2160', 'supportedMainFps': '[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]', 'mainstreamFps': '15', 'rtsp_list': ['rtsp://admin:abcd1234@************:554/cam/realmonitor?channel=1&subtype=0&unicast=true&proto=Onvif', 'rtsp://admin:abcd1234@************:554/cam/realmonitor?channel=1&subtype=1&unicast=true&proto=Onvif'], 'is_ptz': True, 'supportedSubResolution': '[{"width": 1920, "height": 1080}, {"width": 1280, "height": 720}, {"width": 704, "height": 576}]', 'substreamResolution': '704x576', 'supportedSubFps': '[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]', 'substreamFps': '15'}], 'state': True, 'no': 220, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************', 'user': 'admin', 'password': 'abcd1234', 'port': 554, 'brand': 'Dahua', 'model': 'DH-IPC-HDWP3158EIZ5E', 'mac_address': 'f4:b1:c2:b0:36:36', 'serial_number': 'brute_onvif', 'nvr': None, 'number_cameras': 1, 'device_type': 'IPC', 'device_name': '9F079F4PAG3F3DA', 'cam_cfg': [{'profileToken': 'MediaProfile00000', 'supportedMainResolution': '[{"width": 3840, "height": 2160}, {"width": 3072, "height": 2048}, {"width": 3072, "height": 1728}, {"width": 2592, "height": 1944}, {"width": 2688, "height": 1520}, {"width": 2304, "height": 1296}, {"width": 1920, "height": 1080}, {"width": 1280, "height": 960}, {"width": 1280, "height": 720}]', 'mainstreamResolution': '3840x2160', 'supportedMainFps': '[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]', 'mainstreamFps': '15', 'rtsp_list': ['rtsp://admin:abcd1234@************:554/cam/realmonitor?channel=1&subtype=0&unicast=true&proto=Onvif', 'rtsp://admin:abcd1234@************:554/cam/realmonitor?channel=1&subtype=1&unicast=true&proto=Onvif'], 'is_ptz': True, 'supportedSubResolution': '[{"width": 1920, "height": 1080}, {"width": 1280, "height": 720}, {"width": 704, "height": 576}]', 'substreamResolution': '704x576', 'supportedSubFps': '[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]', 'substreamFps': '15'}], 'state': True, 'no': 221, 'total': 256, 'records': [], 'metadata': []}
{'ip': '***********', 'user': None, 'password': None, 'port': 554, 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_credentials', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 222, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************6', 'user': None, 'password': None, 'port': 554, 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_credentials', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 223, 'total': 256, 'records': [], 'metadata': []}
{'ip': '***********', 'user': None, 'password': None, 'port': 554, 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_credentials', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 224, 'total': 256, 'records': [], 'metadata': []}
{'ip': '***********0', 'user': None, 'password': None, 'port': 554, 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_credentials', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 225, 'total': 256, 'records': [], 'metadata': []}
{'ip': '***********8', 'user': None, 'password': None, 'port': 554, 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_credentials', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 226, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************9', 'user': None, 'password': None, 'port': 554, 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_credentials', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 227, 'total': 256, 'records': [], 'metadata': []}
{'ip': '***********7', 'user': None, 'password': None, 'port': 554, 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_credentials', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 228, 'total': 256, 'records': [], 'metadata': []}
{'ip': '***********', 'user': None, 'password': None, 'port': 554, 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_credentials', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 229, 'total': 256, 'records': [], 'metadata': []}
{'ip': '***********5', 'user': None, 'password': None, 'port': 554, 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_credentials', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 230, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************9', 'user': None, 'password': None, 'port': 554, 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_credentials', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 231, 'total': 256, 'records': [], 'metadata': []}
{'ip': '***********1', 'user': None, 'password': None, 'port': 554, 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_credentials', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 232, 'total': 256, 'records': [], 'metadata': []}
{'ip': '***********7', 'user': None, 'password': None, 'port': 554, 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_credentials', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 233, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************3', 'user': None, 'password': None, 'port': 554, 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_credentials', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 234, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************1', 'user': None, 'password': None, 'port': 554, 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_credentials', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 235, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************6', 'user': None, 'password': None, 'port': 554, 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_credentials', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 236, 'total': 256, 'records': [], 'metadata': []}
{'ip': '***********47', 'user': None, 'password': None, 'port': 554, 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_credentials', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 237, 'total': 256, 'records': [], 'metadata': []}
{'ip': '***********3', 'user': None, 'password': None, 'port': 554, 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_credentials', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 238, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************9', 'user': None, 'password': None, 'port': 554, 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_credentials', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 239, 'total': 256, 'records': [], 'metadata': []}
{'ip': '***********4', 'user': None, 'password': None, 'port': 554, 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_credentials', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 240, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************', 'user': None, 'password': None, 'port': 554, 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_credentials', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 241, 'total': 256, 'records': [], 'metadata': []}
{'ip': '***********5', 'user': None, 'password': None, 'port': 554, 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_credentials', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 242, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************9', 'user': None, 'password': None, 'port': 554, 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_credentials', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 243, 'total': 256, 'records': [], 'metadata': []}
{'ip': '***********0', 'user': None, 'password': None, 'port': 554, 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_credentials', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 244, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************4', 'user': None, 'password': None, 'port': 554, 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_credentials', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 245, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************7', 'user': None, 'password': None, 'port': 554, 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_credentials', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 246, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************', 'user': None, 'password': None, 'port': 554, 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_credentials', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 247, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': 554, 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_credentials', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 248, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': 554, 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_credentials', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 249, 'total': 256, 'records': [], 'metadata': []}
{'ip': '*************', 'user': None, 'password': None, 'port': 554, 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_credentials', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 250, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************2', 'user': None, 'password': None, 'port': 554, 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_credentials', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 251, 'total': 256, 'records': [], 'metadata': []}
{'ip': '***********4', 'user': None, 'password': None, 'port': 554, 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_credentials', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 252, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************', 'user': None, 'password': None, 'port': 554, 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_credentials', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 253, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************9', 'user': None, 'password': None, 'port': 554, 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_credentials', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 254, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************', 'user': None, 'password': None, 'port': 554, 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_credentials', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 255, 'total': 256, 'records': [], 'metadata': []}
{'ip': '************', 'user': None, 'password': None, 'port': 554, 'brand': None, 'model': None, 'mac_address': None, 'serial_number': 'brute_credentials', 'nvr': None, 'number_cameras': 1, 'device_type': '', 'device_name': '', 'cam_cfg': [], 'state': False, 'no': 256, 'total': 256, 'records': [], 'metadata': []}
