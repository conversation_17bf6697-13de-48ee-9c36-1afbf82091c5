#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Basic Dahua RPC wrapper.
Forked from https://gist.github.com/48072a72be3a169bc43549e676713201.git
Added ANPR Plate Number extraction by <PERSON><PERSON><PERSON> <https://github.com/naveenrobo>
Example:
    from dahua_rpc import DahuaRpc
    dahua = DahuaRpc(host="************", username="admin", password="password")
    dahua.login()
  # Get the current time on the device
    print(dahua.current_time())
  # Set display to 4 grids with first view group
    dahua.set_split(mode=4, view=1)
  # Make a raw RPC request to get serial number
    print(dahua.request(method="magicBox.getSerialNo"))
  # Get the ANPR Plate Numbers by using the following
    object_id = dahua.get_traffic_info() # Get the object id
    dahua.startFind(object_id=object_id) # Use the object id to find the Plate Numbers
    response = json.dumps(dahua.do_find(object_id=object_id)) # Extract the Plate Numbers
Dependencies:
  pip install requests
"""

import json
import sys
import hashlib
from urllib.error import HTTPError
import requests
from enum import Enum

if (sys.version_info > (3, 0)):
    unicode = str


class DahuaRpc(object):

    def __init__(self, host, username, password):
        self.host = host
        self.username = username
        self.password = password

        self.s = requests.Session()
        self.session_id = None
        self.id = 0

    def request(self, method, params=None, object_id=None, extra=None, url=None):
        """Make a RPC request."""
        self.id += 1
        data = {'method': method, 'id': self.id}
        if params is not None:
            data['params'] = params
        if object_id:
            data['object'] = object_id
        if extra is not None:
            data.update(extra)
        if self.session_id:
            data['session'] = self.session_id
        if not url:
            url = "http://{}/RPC2".format(self.host)
        # print(f'url = {url,data}')
        r = self.s.post(url, json=data, timeout=3)
        return r.json()

    def login(self):
        """Dahua RPC login.
        Reversed from rpcCore.js (login, getAuth & getAuthByType functions).
        Also referenced:
        https://gist.github.com/avelardi/1338d9d7be0344ab7f4280618930cd0d
        """

        # login1: get session, realm & random for real login
        url = 'http://{}/RPC2_Login'.format(self.host)
        method = "global.login"
        params = {'userName': self.username,
                  'password': "",
                  'clientType': "Web3.0"}
        r = self.request(method=method, params=params, url=url)

        self.session_id = r['session']
        realm = r['params']['realm']
        random = r['params']['random']

        # Password encryption algorithm
        # Reversed from rpcCore.getAuthByType
        pwd_phrase = self.username + ":" + realm + ":" + self.password
        if isinstance(pwd_phrase, unicode):
            pwd_phrase = pwd_phrase.encode('utf-8')
        pwd_hash = hashlib.md5(pwd_phrase).hexdigest().upper()
        pass_phrase = self.username + ':' + random + ':' + pwd_hash
        if isinstance(pass_phrase, unicode):
            pass_phrase = pass_phrase.encode('utf-8')
        pass_hash = hashlib.md5(pass_phrase).hexdigest().upper()

        # login2: the real login
        params = {'userName': self.username,
                  'password': pass_hash,
                  'clientType': "Web3.0",
                  'authorityType': "Default",
                  'passwordType': "Default"}
        r = self.request(method=method, params=params, url=url)

        if r['result'] is False:
            raise LoginError(str(r))

    def get_product_def(self):
        method = "magicBox.getProductDefinition"

        params = {
            "name" : "Traffic"
        }
        r = self.request(method=method, params=params)

        if r['result'] is False:
            raise RequestError(str(r))

    def keep_alive(self):
        params = {
            'timeout': 300,
            'active': False
        }

        method = "global.keepAlive"
        r = self.request(method=method, params=params)

        if r['result'] is True:
            return True
        else:
            raise RequestError(str(r))

    def get_traffic_info(self):
        method = "RecordFinder.factory.create"

        params = {
            "name" : "TrafficSnapEventInfo"
        }
        r = self.request(method=method, params=params)
        
        if type(r['result']):
            return r['result']
        else:
            raise RequestError(str(r))
        
    def get_record_finder(self):
        method = "RecordFinder.factory.create"

        params = {
            "name" : "RecordFinder"
        }
        r = self.request(method=method, params=params)
        
        if type(r['result']):
            return r['result']
        else:
            raise RequestError(str(r))

    def start_find(self,object_id):
        method = "RecordFinder.startFind"
        object_id = object_id
        params = {
            "condition" : {
                "Time" : ["<>",1558925818,1559012218]
            }
        }
        r = self.request(object_id=object_id,method=method, params=params)

        if r['result'] is False:
            raise RequestError(str(r))

    def do_find(self,object_id):
        method = "RecordFinder.doFind"
        object_id = object_id
        params = {
            "count" : 50000
        }
        r = self.request(object_id=object_id,method=method, params=params)

        if r['result'] is False:
            raise RequestError(str(r))
        else:
            return r
            
    def set_config(self, params):
        """Set configurations."""

        method = "configManager.setConfig"
        r = self.request(method=method, params=params)

        if r['result'] is False:
            raise RequestError(str(r))

    def reboot(self):
        """Reboot the device."""

        # Get object id
        method = "magicBox.factory.instance"
        params = ""
        r = self.request(method=method, params=params)
        object_id = r['result']

        # Reboot
        method = "magicBox.reboot"
        r = self.request(method=method, params=params, object_id=object_id)

        if r['result'] is False:
            raise RequestError(str(r))

    def current_time(self):
        """Get the current time on the device."""

        method = "global.getCurrentTime"
        r = self.request(method=method)
        if r['result'] is False:
            raise RequestError(str(r))

        return r['params']['time']

    def ntp_sync(self, address, port, time_zone):
        """Synchronize time with NTP."""

        # Get object id
        method = "netApp.factory.instance"
        params = ""
        r = self.request(method=method, params=params)
        object_id = r['result']

        # NTP sync
        method = "netApp.adjustTimeWithNTP"
        params = {'Address': address, 'Port': port, 'TimeZone': time_zone}
        r = self.request(method=method, params=params, object_id=object_id)

        if r['result'] is False:
            raise RequestError(str(r))

    def get_split(self):
        """Get display split mode."""

        # Get object id
        method = "split.factory.instance"
        params = {'channel': 0}
        r = self.request(method=method, params=params)
        object_id = r['result']

        # Get split mode
        method = "split.getMode"
        params = ""
        r = self.request(method=method, params=params, object_id=object_id)

        if r['result'] is False:
            raise RequestError(str(r))

        mode = int(r['params']['mode'][5:])
        view = int(r['params']['group']) + 1

        return mode, view

    def attach_event(self, event = []):
        """Attach a event to current session"""
        method = "eventManager.attach"
        if(event is None):
            return
        params = {
            'codes' : [*event]
        }

        r = self.request(method=method, params=params)

        if r['result'] is False:
            raise RequestError(str(r))

        return r['params']


    def listen_events(self, _callback= None):
        """ Listen for envents. Attach an event before using this function """
        url = "http://{host}/SubscribeNotify.cgi?sessionId={session}".format(host=self.host,session=self.session_id)
        response = self.s.get(url, stream= True)

        buffer = ""
        for chunk in response.iter_content(chunk_size=1):
            buffer += chunk.decode("utf-8")
            if (buffer.endswith('</script>') is True):
                if _callback:
                    _callback(buffer)
                buffer = ""

    def set_split(self, mode, view):
        """Set display split mode."""

        if isinstance(mode, int):
            mode = "Split{}".format(mode)
        group = view - 1

        # Get object id
        method = "split.factory.instance"
        params = {'channel': 0}
        r = self.request(method=method, params=params)
        object_id = r['result']

        # Set split mode
        method = "split.setMode"
        params = {'displayType': "General",
                  'workMode': "Local",
                  'mode': mode,
                  'group': group}
        r = self.request(method=method, params=params, object_id=object_id)

        if r['result'] is False:
            raise RequestError(str(r))

    def get_cookies(self, ip, port, user, password):
        payload = {"method": "global.login", "params": {"userName": "", "password": "", "clientType": "Web3.0"},
                "id": 10000}
        url = f'http://{ip}:{port}/RPC2_Login'
        response = requests.post(url, data=json.dumps(payload, ensure_ascii=False))
        if response.status_code == 200:
            res_json = json.loads(response.text)
            random = res_json['params']['random']
            realm = res_json['params']['realm']
            session = res_json['session']

            password_md5 = hashlib.md5(f'{user}:{realm}:{password}'.encode()).hexdigest().upper()
            params_password_md5 = hashlib.md5(f'{user}:{random}:{password_md5}'.encode()).hexdigest().upper()

            payload['params']['userName'] = 'admin'
            payload['params']['password'] = params_password_md5
            payload['session'] = session

            response = requests.post(url, data=json.dumps(payload, ensure_ascii=False))
            if response.status_code == 200:
                res_json = json.loads(response.text)
                if not res_json['result']:
                    raise HTTPError(url, res_json['error']['code'], res_json['params']['error'], None, None)
                return dict(DHLangCookie30='SimpChinese', DHVideoWHMode='Adaptive Window',
                            DhWebClientSessionID=str(session),
                            DhWebCookie=json.dumps({"username": user, "pswd": "", "talktype": 1, "logintype": 0}))
    
    def get_device_info(self, ip, port, user, password):
        cookies = self.get_cookies(ip, port, user, password)
        session = cookies['DhWebClientSessionID']
        session = int(session) if session.isdigit() else session
        url = f'http://{ip}:{port}/RPC2'
        
        params = {
            "method": "system.multicall",
            "params": [
                {"method": "configManager.getConfig", "params": {"name": "General"}, "session": session, "id": 1},
                {"method": "magicBox.getDeviceType", "params": "", "session": session, "id": 2},
                {"method": "magicBox.getDeviceClass", "params": None, "session": session, "id": 3},
                {"method": "magicBox.getSerialNo", "params": "", "session": session, "id": 4},
                {"method": "configManager.getConfig", "params": {"name": "ChannelTitle"}, "session": session, "id": 5},
                {"method": "configManager.getConfig", "params": {"name": "Encode"}, "session": session, "id": 6},
                {"method": "configManager.getConfig", "params": {"name": "RTSP"}, "session": session, "id": 7},
                {"method": "configManager.getConfig", "params": {"name": "Network"}, "session": session, "id": 8},
                {"method": "LogicDeviceManager.getCameraState", "params": {"uniqueChannels": [-1]}, "session": session, "id": 9}
            ],
            "session": session,
            "id": 10
        }
        
        response = requests.post(url, data=json.dumps(params), cookies=cookies)
        response_json = json.loads(response.text)
        
        # Extract network details
        network_info = response_json['params'][7]['params']['table']
        network = {
            'interface': network_info['DefaultInterface'],
            'domain': network_info['Domain'],
            'hostname': network_info['Hostname'],
            'ipAddress': network_info['eth0']['IPAddress'],
            'subnetMask': network_info['eth0']['SubnetMask'],
            'gateway': network_info['eth0']['DefaultGateway'],
            'dns': network_info['eth0']['DnsServers'],
            'macAddress': network_info['eth0']['PhysicalAddress']
        }
        
        device_info = {
            'deviceName': response_json['params'][0]['params']['table']['MachineName'],
            'model': response_json['params'][1]['params']['type'],
            'deviceType': response_json['params'][2]['params']['type'],
            'serialNumber': response_json['params'][3]['params']['sn'],
            'protocol': {
                'name': 'rtsp',
                'port': response_json['params'][6]['params']['table']['Port']
            },
            'network': network  # Include the network info in device_info
        }
        
        rtsp_port = response_json['params'][6]['params']['table']['Port']
        channels = []
        channel_titles = response_json['params'][4]['params']['table']
        encode_params = response_json['params'][5]['params']['table']
        states_response = response_json['params'][8] if response_json['params'][8]['result'] else None

        for i, channel in enumerate(channel_titles):
            channel_info = {
                'name': channel['Name'],
                'inputPort': i + 1,
                'online': True
            }
            
            if states_response:
                states = states_response['params']['states']
                channel_info['inputPort'] = states[i]['channel']
                channel_info['online'] = states[i].get('connectionState') == 'Connected'

            if encode_params[i] is not None:
                for key, stream in (('mainStream', 'MainFormat'), ('subStream', 'ExtraFormat')):
                    channel_info[key] = {
                        'videoEnable': encode_params[i][stream][0]['VideoEnable'],
                        'vcodec': encode_params[i][stream][0]['Video']['Compression'],
                        'frame': encode_params[i][stream][0]['Video']['FPS'],
                        'audioEnable': encode_params[i][stream][0]['AudioEnable'],
                        'acodec': encode_params[i][stream][0]['Audio']['Compression'],
                    }
                    # Add RTSP URL for the stream
                    subtype = 0 if key == 'mainStream' else 1
                    channel_info[key]['rtspUrl'] = f"rtsp://{ip}:{rtsp_port}/cam/realmonitor?channel={i + 1}&subtype={subtype}"

            channels.append(channel_info)

        device_info['channels'] = channels
        return device_info

    def get_record_video(self, username, password, start_time, end_time, rtsp_url):
        # link rtsp_url: rtsp://<username>:<password>@<ip>:<port>/cam/realmonitor?channel=<ChannelNo>&subtype=<typeNo> 
        # need parser to playback
        # rtsp://<username>:<password>@<ip>:<port>/cam/playback?channel=<ChannelNo>&starttime=<starttime>&endtime=<endtime>
        # example: tsp://admin:admin@***********:554/cam/playback?channel=1&starttime=2012_09_15_12_37_05&endtime=2012_09_15_18_34_14 
        playback_url = rtsp_url.replace("realmonitor", "playback").replace("&subtype=0", "").replace("&subtype=1", "")
        # start_time: 2024-11-19T00:00:00Z to 2024_11_19_00_00_00, T and Z
        start_time_parser = start_time.replace(" ", "_").replace(":", "_").replace("-", "_").replace("T", "_").replace("Z", "")
        # end_time: 2024-11-19T14:30:01Z to 2024_11_19_14_30_01
        end_time_parser = end_time.replace(" ", "_").replace(":", "_").replace("-", "_").replace("T", "_").replace("Z", "")
        playback_url = f"{playback_url}&starttime={start_time_parser}&endtime={end_time_parser}"
        # add username and password
        playback_url = playback_url.replace("rtsp://", f"rtsp://{username}:{password}@")
        return playback_url



class LoginError(Exception):
    pass


class RequestError(Exception):
    pass