"""
hikvision.api
~~~~~~~~~~~~~~~~~~~~

Provides methods for interacting with hikvision

Copyright (c) 2015 Finbar<PERSON> Brady <https://github.com/fbradyirl>
Licensed under the MIT license.
"""

import datetime
import logging
import os
from xml.etree import ElementTree
import re
import xml.etree.ElementTree as ET
import requests
from requests.exceptions import ConnectionError as ReConnError
from requests.auth import HTTPBasicAuth, HTTPDigestAuth
from tqdm import tqdm  # For progress bar
from cam_scan.api.hikvision.error import HikvisionError, MissingParamError
from cam_scan.api.hikvision.constants import DEFAULT_PORT, DEFAULT_HEADERS, XML_ENCODING
from cam_scan.api.hikvision.constants import DEFAULT_SENS_LEVEL

_LOGGING = logging.getLogger(__name__)

# pylint: disable=too-many-arguments
# pylint: disable=too-many-instance-attributes


def build_url_base(host, port, is_https):
    """
    Make base of url based on config
    """
    base = "http"
    if is_https:
        base += 's'

    base += "://"
    base += host

    if port:
        base += ":"
        base += str(port)
    return base


def log_response_errors(response):
    """
    Logs problems in a response
    """

    _LOGGING.error("status_code %s", response.status_code)


def enable_logging():
    """ Setup the logging for home assistant. """
    logging.basicConfig(level=logging.DEBUG)


def remove_namespace(response):
    """ Removes namespace element from xml"""
    return re.sub(' xmlns="[^"]+"', '', response)


def tree_no_ns_from_string(response):
    """ Removes namespace element from response"""
    text = remove_namespace(response)
    return ElementTree.fromstring(text)


class CreateDevice:

    """
    Creates a new camera api device
    """

    def __init__(self, host=None, port=DEFAULT_PORT,
                 username=None, password=None, is_https=False,
                 sensitivity_level=DEFAULT_SENS_LEVEL,
                 digest_auth=True, strict_isapi=True):
        enable_logging()
        # _LOGGING.info("Initialising new hikvision camera client")

        if not host:
            # _LOGGING.error('Missing hikvision host!')
            raise MissingParamError('Connection to hikvision failed.', None)

        if not digest_auth and not is_https:
            pass
            # _LOGGING.warning("%s: HTTP Basic Auth without SSL is insecure",
            #                  host)

        self._username = username
        self._host = host
        self._password = password
        self._sensitivity_level = sensitivity_level
        self._digest_auth = digest_auth
        self._strict_isapi = strict_isapi
        self._auth_fn = HTTPDigestAuth if self._digest_auth else HTTPBasicAuth
        self.xml_motion_detection_off = None
        self.xml_motion_detection_on = None
        self.port = port
        # Now build base url
        self._base = build_url_base(host, self.port, is_https)

        # need to support different channel
        if self._strict_isapi:
            self.motion_url = (
                '%s/ISAPI/System/Video/Inputs/channels/1/motionDetection' %
                self._base)
            self.deviceinfo_url = '%s/ISAPI/System/deviceInfo' % self._base
            # /ISAPI/ContentMgmt/StreamingProxy/channels
            self.channels_useful = '%s/ISAPI/ContentMgmt/StreamingProxy/channels' % self._base
            # /ISAPI/ContentMgmt/InputProxy/channels
            self.channels_inputproxy = '%s/ISAPI/ContentMgmt/InputProxy/channels' % self._base
#            self._xml_namespace = "{http://www.hikvision.com/ver20/XMLSchema}"
            self.record_search =  '%s/ISAPI/ContentMgmt/search' % self._base
        else:
            self.motion_url = '%s/MotionDetection/1' % self._base
            self.deviceinfo_url = '%s/System/deviceInfo' % self._base
            self.channels_useful = '%s/Streaming/channels' % self._base
            self.channels_inputproxy = '%s/Streaming/channels' % self._base
            self.record_search =  '%s/ContentMgmt/search' % self._base
#            self._xml_namespace = "{http://www.hikvision.com/ver10/XMLSchema}"
        self._xml_namespace = ""
        # _LOGGING.info('motion_url: %s', self.motion_url)

        # Required to parse and change xml with the host camera
        # _LOGGING.info(
        #    'ElementTree.register_namespace: %s', self._xml_namespace)
        # ElementTree.register_namespace("", self._xml_namespace)

        try:
            # _LOGGING.info("Going to probe device to test connection")
            # version = self.get_version()
            # enabled = self.is_motion_detection_enabled()
            # _LOGGING.info("%s Connected OK! firmware = %s, "
            #               "motion detection enabled = %s", self._host,
            #               version, enabled)
            pass

        except ReConnError as conn_err:
            print(f'Error: {conn_err}')
            raise HikvisionError('Connection to hikvision %s failed' %
                                 self._host, conn_err) from conn_err
        
        # print(f'CreateDevice: {self._host} - {self._base}')

    def get_version(self):
        """
        Returns the firmware version running on the camera
        """
        return self.get_about(element_to_query='firmwareVersion')


    def get_about(self, element_to_query=None):
        """
        Returns ElementTree containing the result of
        <host>/System/deviceInfo
        or if element_to_query is not None, the value of that element
        """
        # return status_code, text, xml
    
        # _LOGGING.info('url: %s', self.deviceinfo_url)
        response = requests.get(
            self.deviceinfo_url,
            auth=self._auth_fn(self._username, self._password), timeout=3)

        # _LOGGING.debug('response: %s', response)
        # _LOGGING.debug("status_code %s", response.status_code)
        if response.status_code != 200:
            # log_response_errors(response)
            return "status_code", response.status_code

        if element_to_query is None:
            return "text", response.text
        try:
            # print(f'Response: {response.text}')
            tree = tree_no_ns_from_string(response.text)

            element_to_query = './/%s%s' % (
                self._xml_namespace, element_to_query)
            result = tree.findall(element_to_query)
            if len(result) > 0:
                # _LOGGING.debug('element_to_query: %s result: %s',
                #                element_to_query, result[0])

                return "xml", result[0].text.strip()
            _LOGGING.error(
                'There was a problem finding element: %s',
                element_to_query)
            _LOGGING.error('Entire response: %s', response.text)

        except Exception as e:
            _LOGGING.error('Entire response: %s',e)
            # _LOGGING.error(
            #     'There was a problem finding element:'
            #     ' %s AttributeError: %s', element_to_query, attib_err)
            return None, None
        return None, None
    
    def get_channels_useful(self):
        """
        Returns a list of channel IDs from the NVR.
        """

        _LOGGING.info('url: %s', self.channels_useful)

        response = requests.get(
            self.channels_useful,
            auth=self._auth_fn(self._username, self._password),
            timeout=3
        )

        if response.status_code != 200:
            # log_response_errors(response)
            return None

        try:
            tree = tree_no_ns_from_string(response.text)
            channel_ids = tree.findall('.//id')
            print(f'Channel IDs: {channel_ids}')
            return [channel.text for channel in channel_ids]
        except Exception as e:
            _LOGGING.error("Error parsing channels: %s", e)
            pass
        return None

        

    def is_motion_detection_enabled(self):
        """Get current state of Motion Detection.

        Returns False on error or if motion detection is off."""

        response = requests.get(self.motion_url, auth=self._auth_fn(
            self._username, self._password), timeout=3)
        # _LOGGING.debug('Response: %s', response.text)

        if response.status_code != 200:
            # _LOGGING.error(
            #     "%s: Error connecting to %s: status_code = %s",
            #     self._host, self.motion_url, response.status_code)
            return False

        try:

            tree = tree_no_ns_from_string(response.text)
            enabled_element = tree.findall(
                './/%senabled' % self._xml_namespace)
            sensitivity_level_element = tree.findall(
                './/%ssensitivityLevel' % self._xml_namespace)
            if len(enabled_element) == 0:
                _LOGGING.error("%s: Problem getting motion detection status",
                               self._host)
                return False
            if len(sensitivity_level_element) == 0:
                _LOGGING.error("%s: Problem getting sensitivityLevel status",
                               self._host)
                return False

            result = enabled_element[0].text.strip()
            # _LOGGING.info(
            #     '%s motion detection state, enabled: %s', self._host, result)

            if int(sensitivity_level_element[0].text) == 0:
                # _LOGGING.warning(
                #     "%s sensitivityLevel is 0.", self._host)
                sensitivity_level_element[0].text = str(
                    self._sensitivity_level)
                # _LOGGING.info(
                #     "%s sensitivityLevel now set to %s",
                #     self._host, self._sensitivity_level)

            if result == 'true':
                # Save this for future switch off
                self.xml_motion_detection_on = ElementTree.tostring(
                    tree, encoding=XML_ENCODING)
                enabled_element[0].text = 'false'
                self.xml_motion_detection_off = ElementTree.tostring(
                    tree, encoding=XML_ENCODING)
                return True
            # Save this for future switch on
            self.xml_motion_detection_off = ElementTree.tostring(
                tree, encoding=XML_ENCODING)
            enabled_element[0].text = 'true'
            self.xml_motion_detection_on = ElementTree.tostring(
                tree, encoding=XML_ENCODING)
            return False

        except AttributeError as attib_err:
            # _LOGGING.error(
            #     '%s: Problem parsing '
            #     'camera motion detection state: %s', self._host, attib_err)
            return False

    def enable_motion_detection(self):
        """ Enable Motion Detection """

        self.put_motion_detection_xml(self.xml_motion_detection_on)

    def disable_motion_detection(self):
        """ Disable Motion Detection """

        self.put_motion_detection_xml(self.xml_motion_detection_off)

    def put_motion_detection_xml(self, xml):
        """ Put request with xml Motion Detection """

        _LOGGING.debug('xml:')
        _LOGGING.debug("%s", xml)

        headers = DEFAULT_HEADERS
        headers['Content-Length'] = str(len(xml))
        headers['Host'] = self._host
        response = requests.put(self.motion_url, auth=self._auth_fn(
            self._username, self._password), data=xml, headers=headers)
        # _LOGGING.debug('request.headers:')
        # _LOGGING.debug('%s', response.request.headers)
        # _LOGGING.debug('Response:')
        # _LOGGING.debug('%s', response.text)

        if response.status_code != 200:
            # _LOGGING.error(
            #     "%s: Error connecting to %s: status_code = %s",
            #     self._host, self.motion_url, response.status_code)
            return

        try:
            tree = tree_no_ns_from_string(response.text)
            enabled_element = tree.findall(
                './/%sstatusString' % self._xml_namespace)
            if len(enabled_element) == 0:
                _LOGGING.error("%s: Problem getting motion detection status",
                               self._host)
                return

            if enabled_element[0].text.strip() == 'OK':
                _LOGGING.info('Updated successfully')

        except AttributeError as attrib_err:
            _LOGGING.error(
                '%s: Problem parsing the response: %s',
                self._host, attrib_err)
            return


    def get_cameras_info(self):
        """
        Returns a list of channel IDs from the NVR.
        """

        _LOGGING.info('url: %s', self.channels_inputproxy)

        response = requests.get(
            self.channels_inputproxy,
            auth=self._auth_fn(self._username, self._password),
            timeout=3
        )

        if response.status_code != 200:
            # log_response_errors(response)
            return None

        try:
            # print('\n get_channels_inputproxy \n')
            # print(f'Response: {response.text}') 
            # response.text = ...
            # <InputProxyChannel version="1.0" xmlns="http://www.hikvision.com/ver20/XMLSchema">
            # <id>7</id>
            # <name>Camera 01</name>
            # <sourceInputPortDescriptor>
                # <proxyProtocol>HIKVISION</proxyProtocol>
                # <addressingFormatType>ipaddress</addressingFormatType>
                # <ipAddress>*************</ipAddress>
                # <managePortNo>8000</managePortNo>
                # <srcInputPort>1</srcInputPort>
                # <userName>admin</userName>
                # <streamType>auto</streamType>
                # <model>DS-2CD1027G0-L</model>
                # <serialNumber>DS-2CD1027G0-L20230413AAWRAA2414630</serialNumber>
                # <firmwareVersion>V5.7.12 build 220819</firmwareVersion>
                # <deviceID></deviceID>
            # </sourceInputPortDescriptor>
            # <enableTiming>true</enableTiming>
            # <devIndex>d6145c98-a67c-4e75-94f8-c9261bceb50f</devIndex>
            # </InputProxyChannel>
            tree = tree_no_ns_from_string(response.text)
            # get id, name, proxyProtocol, model, serialNumber, firmwareVersion, model, ipAddress, managePortNo, userName
            # get object InputProxyChannel and parser to list dict
            input_proxy_channel = tree.findall('.//InputProxyChannel')
            # print(f'input_proxy_channel: {input_proxy_channel}')
            list_cameras = []
            for channel in input_proxy_channel:
                # print(f'channel: {channel}')
                channel_dict = {}
                for child in channel:
                    if child.tag == 'id':
                        # append 1 to 101, 2 to 201, 3 to 301
                        channel_dict[child.tag] = child.text + '01'
                    else:
                        channel_dict[child.tag] = child.text
                    if child.tag == 'sourceInputPortDescriptor':
                        for sub_child in child:
                            channel_dict[sub_child.tag] = sub_child.text
                # print(f'channel_dict: {channel_dict}')
                list_cameras.append(channel_dict)
            return list_cameras
        except AttributeError as attib_err:
            _LOGGING.error("Error parsing channels: %s", attib_err)
        
        return None

    def get_rtsp_url(self, channel_id):
        if channel_id is None:
            return None, None
        # mainstream and substream, append username and password
        main_stream = f'rtsp://{self._username}:{self._password}@{self._host}/Streaming/Channels/{channel_id}'
        # channel_id = 101 -> channel_id_sub = 102
        channel_id_sub = int(channel_id) + 1
        sub_stream = f'rtsp://{self._username}:{self._password}@{self._host}/Streaming/Channels/{channel_id_sub}'
        return main_stream, sub_stream
    

# Response: <?xml version="1.0" encoding="UTF-8" ?>
# <DeviceInfo version="1.0" xmlns="http://www.hikvision.com/ver20/XMLSchema">
# <deviceName>Network Video Recorder</deviceName>
# <deviceID>48443533-3836-3937-3637-686dbcf04d48</deviceID>
# <model>DS-7616NI-K1(B)</model>
# <serialNumber>DS-7616NI-K1(B)1620190824CCRRD53869767WCVU</serialNumber>
# <macAddress>68:6d:bc:f0:4d:48</macAddress>
# <firmwareVersion>V3.4.100</firmwareVersion>
# <firmwareReleasedDate>build 190815</firmwareReleasedDate>
# <encoderVersion>V5.0</encoderVersion>
# <encoderReleasedDate>build 171115</encoderReleasedDate>
# <deviceType>IPC</deviceType>
# <telecontrolID>255</telecontrolID>
# <customizedInfo>DZR20190814001</customizedInfo>
# </DeviceInfo>
# <DeviceInfo version="1.0" xmlns="http://www.hikvision.com/ver20/XMLSchema">
# <deviceName>Victory</deviceName>
# <deviceID>48453634-3739-3331-3832-accb51ed29d2</deviceID>
# <model>DS-7104HQHI-K1</model>
# <serialNumber>DS-7104HQHI-K10420200730CCWRE64793182WCVU</serialNumber>
# <macAddress>ac:cb:51:ed:29:d2</macAddress>
# <firmwareVersion>V4.21.100</firmwareVersion>
# <firmwareReleasedDate>build 200307</firmwareReleasedDate>
# <encoderVersion>V5.0</encoderVersion>
# <encoderReleasedDate>build 200116</encoderReleasedDate>
# <deviceType>IPC</deviceType>
# <telecontrolID>255</telecontrolID>
# <hardwareVersion>0xc0e4220</hardwareVersion>
# </DeviceInfo>

    # Functions to retrieve specific elements
    def get_device_name(self, xml_data):
        device_id = device_name = model = mac_address = serial_number = device_type = None
        metadata = {}
        try:
            # Parse the XML
            root = ET.fromstring(xml_data)
            # Define the namespace
            ns = {'ns': 'http://www.hikvision.com/ver20/XMLSchema'}
            def safe_find(tag):
                try:
                    element = root.find(f'ns:{tag}', ns)
                    return element.text if element is not None else None
                except Exception as e:
                    return None
            # Extract fields safely
            device_id = safe_find('deviceID')
            device_name = safe_find('deviceName')
            model = safe_find('model')
            mac_address = safe_find('macAddress')
            serial_number = safe_find('serialNumber')
            device_type = safe_find('deviceType')
            # Metadata dictionary
            metadata = {
                "firmwareVersion": safe_find('firmwareVersion'),
                "firmwareReleasedDate": safe_find('firmwareReleasedDate'),
                "encoderVersion": safe_find('encoderVersion'),
                "encoderReleasedDate": safe_find('encoderReleasedDate'),
                "telecontrolID": safe_find('telecontrolID'),
                "customizedInfo": safe_find('customizedInfo')  # This will return None since it's not in the XML
            }
        except Exception as e:
            pass
        return device_id, device_name, model, mac_address, serial_number, device_type, metadata


    def get_record_video(self, start_time, end_time, result_position=0, max_results=1, 
                     search_id='812F04E0-4089-11A3-9A0C-0305E82C2906', track_id='101'):
        """
        Fetch recorded video metadata for a specific time range.

        :param start_time: Start time of the recording.
        :param end_time: End time of the recording.
        :param result_position: Position to start fetching results.
        :param max_results: Maximum number of results to fetch.
        :param search_id: Unique identifier for the search.
        :param track_id: Track ID for the camera stream.
        :return: Dictionary with video metadata or None if an error occurs.
        """
        try:
            # parser start time end time 2024-11-15 00:00:00 to 2024-11-15T00:00:00Z
            # check start time and end time correct format %Y-%m-%dT%H:%M:%SZ
            format = '%Y-%m-%dT%H:%M:%SZ'
            if not datetime.datetime.strptime(start_time, format):
                print(f"Error: Invalid start time format: {start_time}")
                return None
            if not datetime.datetime.strptime(end_time, format):
                print(f"Error: Invalid end time format: {end_time}")
                return None
            
            # Prepare XML data for the POST request
            xml_data = f'''<CMSearchDescription version="2.0" xmlns="http://www.isapi.org/ver20/XMLSchema">
                <searchID>{search_id}</searchID>
                <trackIDList>
                    <trackID>{track_id}</trackID>
                </trackIDList>
                <timeSpanList>
                    <timeSpan>
                        <startTime>{start_time}</startTime>
                        <endTime>{end_time}</endTime>
                    </timeSpan>
                </timeSpanList>
                <searchResultPostion>{result_position}</searchResultPostion>
                <maxResults>{max_results}</maxResults>
                <contentTypeList>
                    <contentType>video</contentType>
                </contentTypeList>
            </CMSearchDescription>'''
            # print(f'get_record_video: {xml_data} \nself.record_search: {self.record_search}')

            # Make the request
            response = requests.post(
                self.record_search,
                data=xml_data,
                auth=self._auth_fn(self._username, self._password),
                headers={'Content-Type': 'application/xml'},
                timeout=4
            )

            # Check response status
            if response.status_code != 200:
                print(f"Error: HTTP {response.status_code} - {response.reason}")
                return None

            # Parse the XML response
            ns = {'ns': 'http://www.hikvision.com/ver20/XMLSchema'}
            root = ET.fromstring(response.content)
            
            # Helper function to safely fetch text from XML elements
            def safe_find(element, tag):
                child = element.find(tag, ns)
                return child.text if child is not None else None

            # Initialize the result dictionary
            result = {
                "responseStatus": safe_find(root, "ns:responseStatus"),
                "responseStatusStrg": safe_find(root, "ns:responseStatusStrg"),
                "numOfMatches": int(safe_find(root, "ns:numOfMatches") or 0),
                "matchList": []
            }

            # Process each match item
            for match_item in root.findall(".//ns:searchMatchItem", ns):
                match_dict = {
                    "sourceID": safe_find(match_item, "ns:sourceID"),
                    "trackID": int(safe_find(match_item, "ns:trackID") or 0),
                    "timeSpan": {
                        "startTime": safe_find(match_item, ".//ns:startTime"),
                        "endTime": safe_find(match_item, ".//ns:endTime")
                    },
                    "mediaSegmentDescriptor": {
                        "contentType": safe_find(match_item, ".//ns:contentType"),
                        "codecType": safe_find(match_item, ".//ns:codecType"),
                        "playbackURI": safe_find(match_item, ".//ns:playbackURI")
                    }
                }
                result["matchList"].append(match_dict)
            return result

        except Exception as e:
            print(f"Error: {e}")
            return None

    
    # rtsp://*************/Streaming/tracks/101/?starttime=20241115T000900Z&endtime=20241115T011619Z&name=00000001832000000&size=1065049116
    # rtsp://*************/Streaming/tracks/101/?starttime=20241114T050545Z&endtime=20241115T024435Z&name=00000000362000000&size=1060901100
    # rtsp://*************/Streaming/tracks/101/?starttime=20241115T004625Z&endtime=20241115T020531Z&name=00000000623000000&size=1064583660
    # rtsp://**************/Streaming/tracks/101/?starttime=20241114T233404Z&endtime=20241115T014954Z&name=00000003679000000&size=1063098416
    # ***********
    def download_record_video(self, host, playback_uri, start_time, end_time, name, size, output_dir):
        # Construct the download URL
        # /ISAPI/ContentMgmt/download?playbackURI=rtsp://**********/Streaming/tracks/101?starttime=2020-12-17%2016:48:14Z&amp;endtime=2020-1217%2016:58:55Z&amp;name=00010000029000000&amp;size=322602016
        base_url = f'http://{host}/ISAPI/ContentMgmt/download?playbackURI={playback_uri}&starttime={start_time}&endtime={end_time}&name={name}&size={size}'

        # Ensure output directory exists
        os.makedirs(output_dir, exist_ok=True)

        # Prepare the output file path
        output_file = os.path.join(output_dir, f'{name}.mp4')  # You can adjust the file extension if needed

        try:
            # Send GET request for downloading video content
            response = requests.get(
                                self.record_search,
                                auth=self._auth_fn(self._username, self._password),
                                stream=True,
                                timeout=4)
            print(f"Downloading video from {base_url} to {output_file} - Response: {response.status_code}")
            # Check if the request was successful
            if response.status_code == 200:
                # Get total file size for progress bar
                total_size = int(response.headers.get('Content-Length', 0))

                # Create a progress bar
                with tqdm(total=total_size, unit='B', unit_scale=True, desc=name) as pbar:
                    # Write the response content to a file
                    with open(output_file, 'wb') as f:
                        for chunk in response.iter_content(chunk_size=1024):
                            if chunk:
                                f.write(chunk)
                                pbar.update(len(chunk))  # Update the progress bar

                print(f"Download complete: {output_file}")
            else:
                print(f"Failed to download video. Status code: {response.status_code}, Error: {response.text}")
        except requests.RequestException as e:
            print(f"An error occurred while downloading: {e}")
        pass