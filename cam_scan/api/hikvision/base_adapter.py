import logging
import os
from ctypes import *

from cam_scan.api.hikvision.hkws.config import Config
from cam_scan.api.hikvision.hkws.model import base, alarm
from pathlib import Path
from cam_scan.api.hikvision.hkws.core import env


class BaseAdapter:
    # 动态sdk文件 .so .dll
    so_list = []
    def __init__(self):
        print('init BaseAdapter')
        if env.isLinux():
            print('init BaseAdapter linux')
            self.path = self.create_path_linux()
        else:
            print('init BaseAdapter windows')
            self.path = self.create_path_windows()
        self.common_start()

    def create_path_linux(self):
        # folder = Path.cwd()
        current_dir = os.path.dirname(os.path.abspath(__file__))
        # lib_path = current_dir / "cam_scan" / "api" / "hikvision" / "sdk" / "lib" / "linux"
        lib_path = os.path.join(current_dir, "sdk" , "lib", "linux") + "/"
        return lib_path
    
    def create_path_windows(self):
        # folder = Path.cwd()
        current_dir = os.path.dirname(os.path.abspath(__file__))
        print(f'current_dir = {current_dir}')
        # lib_path = current_dir / "cam_scan" / "api" / "hikvision" / "sdk" / "lib" / "window"
        lib_path = os.path.join(current_dir, "sdk" , "lib", "windows")
        return lib_path
    
    def set_lib(self, so_list: []):
        self.so_list = so_list

    def get_lib(self):
        return self.so_list

    def common_start(self):
        self.get_suffix()
        userId = -1
        self.add_lib(self.path)
        if len(self.so_list) == 0:
            return userId
        if not self.init_sdk():
            return userId
        # print(f"SDK初始化成功 - common_start - {cnf.IP} - {cnf.Port} - {cnf.User} - {cnf.Password}")
        # userId = self.login(cnf.IP, cnf.Port, cnf.User, cnf.Password)
        # if userId < 0:
        #     self.print_error("common_start 失败: the error code is")
        # return userId
    
    def login(self, address="***********", port=8000, user="admin", pwd="admin"):
        # 设置连接时间
        set_overtime = self.call_cpp("NET_DVR_SetConnectTime", 5000, 4)  # 设置超时
        if not set_overtime:
            self.print_error("NET_DVR_SetConnectTime 设置超时错误信息失败：the error code is ")
            return False
        # 设置重连
        self.call_cpp("NET_DVR_SetReconnect", 10000, True)

        b_address = bytes(address, "ascii")
        b_user = bytes(user, "ascii")
        b_pwd = bytes(pwd, "ascii")

        struLoginInfo = base.NET_DVR_USER_LOGIN_INFO()
        struLoginInfo.bUseAsynLogin = 0  # 同步登陆
        i = 0
        for o in b_address:
            struLoginInfo.sDeviceAddress[i] = o
            i += 1

        struLoginInfo.wPort = port
        i = 0
        for o in b_user:
            struLoginInfo.sUserName[i] = o
            i += 1

        i = 0
        for o in b_pwd:
            struLoginInfo.sPassword[i] = o
            i += 1

        device_info = base.NET_DVR_DEVICEINFO_V40()
        loginInfo1 = byref(struLoginInfo)
        loginInfo2 = byref(device_info)
        user_id = self.call_cpp("NET_DVR_Login_V40", loginInfo1, loginInfo2)
        if user_id == -1:  # -1表示失败，其他值表示返回的用户ID值。
            self.print_error("NET_DVR_Login_V40 用户登录失败: the error code is ")
        return user_id

        # 设备登出
    # 加载目录下所有so文件
    def add_lib(self, path):
        self.get_suffix()
        print(f'add_lib = {path}')
        files = os.listdir(path)
        for file in files:
            # print(f'aaaaaa = {type(path),type(suffix)}')  
            if not os.path.isdir(str(Path(path) / file)):
                
                if file.endswith(self.Suffix):
                    self.so_list.append(str(Path(path) / file))
            else:
                # print(f'add_lib = {path,file}')
                self.add_lib(str(Path(path) / file) + "/")
        # print(f'aaaaaa = {len(self.so_list)}')     

    def get_suffix(self):
        if env.isWindows():
            self.Suffix = ".dll"
        else:
            self.Suffix = ".so"

    def init_sdk(self):
        init_res = self.call_cpp("NET_DVR_Init")  # SDK初始化
        # print(f"SDK NET_DVR_Init - {init_res}")
        if init_res:
            # logging.info("SDK initialization successful")
            return True
        else:
            # self.print_error("NET_DVR_GetLastError 初始化SDK失败: the error code is ")
            return False
        
    def get_sdk_version(self):
        return self.call_cpp("NET_DVR_GetSDKVersion")
    
    def call_cpp(self, func_name, *args):
        
        for so_lib in self.so_list:
            try:
                lib = cdll.LoadLibrary(so_lib)
                try:
                    value = eval("lib.%s" % func_name)(*args)
                    # print(f'func_name：{func_name,so_lib}')
                    # logging.info("func_name：" + so_lib)
                    # logging.info("执行成功,返回值：" + str(value))
                    return value
                except:
                    # print(f'call_cpp = {func_name}')
                    continue
            except:
                print(f'call_cpp1 = {func_name}')
                continue

        logging.error("没有找到接口！")
        return False
    
    def print_error(self, msg=""):
        error_info = self.call_cpp("NET_DVR_GetLastError")
        logging.error(msg + str(error_info))

    def get_device_configuration(self, user_id, channel=2):
        # Set up the output buffer size for the device configuration
        device_info = base.NET_DVR_DEVICECFG_V50()
        out_buffer_size = sizeof(base.NET_DVR_DEVICECFG_V50)
        # print(f"out_buffer_size: {out_buffer_size}")
        bytes_returned = c_ulong(0)
        NET_DVR_GET_DEVICECFG_V50=3801
        # Call the NET_DVR_GetDVRConfig function to get the device configuration
        # print(f"Retrieving device configuration for user_id {user_id} - channel {channel}...")
        result = self.call_cpp("NET_DVR_GetDVRConfig", user_id, NET_DVR_GET_DEVICECFG_V50, channel, byref(device_info), out_buffer_size, byref(bytes_returned))

        # print(f"NET_DVR_GetDVRConfig result: {result}")
        if result == -1:
            # self.print_error("NET_DVR_GetDVRConfig: the error code is ")
            return False
        # else:
        #     sSerialNumber = device_info.sSerialNumber
        #     print("sSerialNumber:", bytes(sSerialNumber).decode('utf-8', errors='ignore').strip('\x00'))
        #     sDVRName = device_info.sDVRName
        #     print("sDVRName:", bytes(sDVRName).decode('utf-8', errors='ignore').strip('\x00'))
        #     byDevTypeName = device_info.byDevTypeName
        #     print("byDevTypeName:", bytes(byDevTypeName).decode('utf-8', errors='ignore').strip('\x00'))

        return device_info
    
    def sdk_clean(self):
        result = self.call_cpp("NET_DVR_Cleanup")
        logging.info("释放资源", result)

    def logout(self, userId=0):
        result = self.call_cpp("NET_DVR_Logout", userId)
        logging.info("登出", result)

    def get_network_configuration(self, user_id, channel=1):
        # Set up the output buffer size for the network configuration
        network_info = base.NET_DVR_NETCFG_V50()  # Ensure this struct is correctly defined
        out_buffer_size = sizeof(base.NET_DVR_NETCFG_V50)
        # print(f"out_buffer_size: {out_buffer_size}")
        bytes_returned = c_ulong(0)
        # NET_DVR_GET_NETCFG_V50 = 1009  # Example code, replace with actual code from SDK
        

        # Call the NET_DVR_GetDVRConfig function to get the network configuration
        # print(f"Retrieving network configuration for user_id {user_id} - channel {channel}...")
        result = self.call_cpp("NET_DVR_GetDVRConfig", user_id, base.NET_DVR_GET_NETCFG_V50, channel, byref(network_info), out_buffer_size, byref(bytes_returned))

        # print(f"NET_DVR_GetDVRConfig result: {result}")
        if result == -1:
            # self.print_error("NET_DVR_GetDVRConfig: the error code is ")
            return False
        else:
            # print("Network configuration retrieved successfully.")
            
            # Print or process each relevant field in network_info
            # Assuming fields like 'sIpAddress', 'byMACAddr', etc., exist in NET_DVR_NETCFG_V50
            # print(f'network_info: {network_info}')
            # struEtherNet
            # Access the struEtherNet array from network_info
            struEtherNet = network_info.struEtherNet

            # Iterate through each NET_DVR_ETHERNET_V30 object in the struEtherNet array
            # for i, ethernet_info in enumerate(struEtherNet):
            #     mac_address = ":".join(f"{b & 0xFF:02X}" for b in ethernet_info.byMACAddr)
            #     print(f"Ethernet Port {i}:")
            #     print(f"  IP Address: {ethernet_info.struDVRIP.sIpV4.decode('utf-8')}")
            #     print(f"  IP Mask: {ethernet_info.struDVRIPMask.sIpV4.decode('utf-8')}")
            #     print(f"  Network Interface: {ethernet_info.dwNetInterface}")
            #     print(f"  Port: {ethernet_info.wDVRPort}")
            #     print(f"  MTU: {ethernet_info.wMTU}")
            #     print(f"  MAC Address: {mac_address}")
            #     print(f"  Ethernet Port No: {ethernet_info.byEthernetPortNo}")

            # # Loop through all fields to display them, if necessary
            # print("\nAll parameters in network configuration:")
            # for field_name, field_type in network_info._fields_:
            #     value = getattr(network_info, field_name)
            #     if isinstance(value, bytes):
            #         value = value.decode('utf-8', errors='ignore').strip('\x00')
            #     print(f"{field_name}: {value}")
            return struEtherNet
        
base_adapter = BaseAdapter()
