import logging
import os
from ctypes import *

from hkws.config import Config
from hkws.model import base, alarm


# 海康威视基础类，AI摄像机，通用摄像机，门禁产品，出入口产品通用
class BaseAdapter:
    # 动态sdk文件 .so .dll
    so_list = []

    def set_lib(self, so_list: []):
        self.so_list = so_list

    def get_lib(self):
        return self.so_list

    # 常规启动，初始化SDK到用户注册设备
    def common_start(self, cnf: Config):
        userId = -1
        self.add_lib(cnf.SDKPath, cnf.Suffix)
        if len(self.so_list) == 0:
            return userId
        if not self.init_sdk():
            return userId
        print(f"SDK初始化成功 - common_start - {cnf.IP} - {cnf.Port} - {cnf.User} - {cnf.Password}")
        userId = self.login(cnf.IP, cnf.Port, cnf.User, cnf.Password)
        if userId < 0:
            self.print_error("common_start 失败: the error code is")
        return userId

    # 加载目录下所有so文件
    def add_lib(self, path, suffix):
        files = os.listdir(path)

        for file in files:
            if not os.path.isdir(path + file):
                if file.endswith(suffix):
                    self.so_list.append(path + file)
            else:
                self.add_lib(path + file + "/", suffix)
                
    # python 调用 sdk 指定方法
    def call_cpp(self, func_name, *args):
        for so_lib in self.so_list:
            try:
                lib = cdll.LoadLibrary(so_lib)
                try:
                    value = eval("lib.%s" % func_name)(*args)
                    logging.info("调用的库：" + so_lib)
                    logging.info("执行成功,返回值：" + str(value))
                    return value
                except:
                    continue
            except:
                # logging.info("库文件载入失败：" + so_lib)
                continue

        logging.error("没有找到接口！")
        return False

    # 初始化海康微视 sdk
    def init_sdk(self):
        init_res = self.call_cpp("NET_DVR_Init")  # SDK初始化
        print(f"SDK初始化 init_res - {init_res}")
        if init_res:
            logging.info("SDK initialization successful")
            return True
        else:
            self.print_error("NET_DVR_GetLastError 初始化SDK失败: the error code is ")
            return False

    # 设置sdk初始化参数
    def set_sdk_config(self, enumType, sdkPath):
        req = base.NET_DVR_LOCAL_SDK_PATH()
        sPath = bytes(sdkPath, "ascii")
        i = 0
        for o in sPath:
            req.sPath[i] = o
            i += 1

        ptr = byref(req)
        res = self.call_cpp("NET_DVR_SetSDKInitCfg", enumType, ptr)
        if res < 0:
            self.print_error("NET_DVR_SetSDKInitCfg 启动预览失败: the error code is")
        return res

    # 释放sdk
    def sdk_clean(self):
        result = self.call_cpp("NET_DVR_Cleanup")
        logging.info("释放资源", result)

    # 设备登录
    def login(self, address="***********", port=8000, user="admin", pwd="admin"):
        # 设置连接时间
        set_overtime = self.call_cpp("NET_DVR_SetConnectTime", 5000, 4)  # 设置超时
        if not set_overtime:
            self.print_error("NET_DVR_SetConnectTime 设置超时错误信息失败：the error code is ")
            return False
        # 设置重连
        self.call_cpp("NET_DVR_SetReconnect", 10000, True)

        b_address = bytes(address, "ascii")
        b_user = bytes(user, "ascii")
        b_pwd = bytes(pwd, "ascii")

        struLoginInfo = base.NET_DVR_USER_LOGIN_INFO()
        struLoginInfo.bUseAsynLogin = 0  # 同步登陆
        i = 0
        for o in b_address:
            struLoginInfo.sDeviceAddress[i] = o
            i += 1

        struLoginInfo.wPort = port
        i = 0
        for o in b_user:
            struLoginInfo.sUserName[i] = o
            i += 1

        i = 0
        for o in b_pwd:
            struLoginInfo.sPassword[i] = o
            i += 1

        device_info = base.NET_DVR_DEVICEINFO_V40()
        loginInfo1 = byref(struLoginInfo)
        loginInfo2 = byref(device_info)
        user_id = self.call_cpp("NET_DVR_Login_V40", loginInfo1, loginInfo2)
        if user_id == -1:  # -1表示失败，其他值表示返回的用户ID值。
            self.print_error("NET_DVR_Login_V40 用户登录失败: the error code is ")
        return user_id

        # 设备登出

    def logout(self, userId=0):
        result = self.call_cpp("NET_DVR_Logout", userId)
        logging.info("登出", result)

    # 设置报警回调函数
    def setup_alarm_chan_v31(self, cbFunc, user_id):
        result = self.call_cpp("NET_DVR_SetDVRMessageCallBack_V31", cbFunc, user_id)
        if result == -1:
            self.print_error(
                "NET_DVR_SetDVRMessageCallBack_V31 初始化SDK失败: the error code is "
            )
        return result

    # 设置报警布防
    def setup_alarm_chan_v41(self, user_id=0):
        structure_l = alarm.NET_DVR_SETUPALARM_PARAM()
        structure_l.dwSize = sizeof(structure_l)
        structure_l.byFaceAlarmDetection = 0
        structure_l_ref = byref(structure_l)
        result = self.call_cpp("NET_DVR_SetupAlarmChan_V41", user_id, structure_l_ref)
        if result == -1:
            self.print_error("NET_DVR_SetupAlarmChan_V41 报警布防: the error code is ")
        return result

    # 报警撤防
    def close_alarm(self, alarm_result):
        return self.call_cpp("NET_DVR_CloseAlarmChan_V30", alarm_result)

    # 获取SDK版本，2个高字节表示主版本，2个低字节表示次版本。如0x00030000：表示版本为3.0。
    def get_sdk_version(self):
        return self.call_cpp("NET_DVR_GetSDKVersion")

    # 获取SDK的版本号和Build信息
    # SDK的版本号和build信息。2个高字节表示版本号 ：25~32位表示主版本号，17~24位表示次版本号；2个低字节表示build信息。 如0x03000101：表示版本号为3.0，build 号是0101。
    def get_sdk_build_version(self):
        return self.call_cpp("NET_DVR_GetSDKBuildVersion")

    # 获取当前SDK状态信息失败
    def get_sdk_state(self):
        op = base.NET_DVR_SDKSTATE()
        pSDKState = byref(op)
        res = self.call_cpp("NET_DVR_GetSDKState", pSDKState)
        if not res:
            self.print_error("NET_DVR_GetSDKState 获取当前SDK状态信息失败: the error code is ")
        return res, op

    # 获取当前SDK的功能信息
    def get_sdk_abl(self):
        op = base.NET_DVR_SDKABL()
        pSDKAbl = byref(op)
        res = self.call_cpp("NET_DVR_GetSDKAbility", pSDKAbl)
        if not res:
            self.print_error("NET_DVR_GetSDKAbility 获取当前SDK功能信息失败: the error code is ")
        return res, op

    # 激活设备
    def activate_device(self, ip="***********", port=8000, pwd="123456"):
        print(f"激活设备 - {ip} - {port} - {pwd}")
        b_ip = bytes(ip, "ascii")
        b_pwd = bytes(pwd, "ascii")

        input = base.NET_DVR_ACTIVATECFG()
        input.dwSize = sizeof(input)

        i = 0
        for o in b_pwd:
            input.sPassword[i] = o
            i += 1

        input_ref = byref(input)
        res = self.call_cpp("NET_DVR_ActivateDevice", b_ip, port, input_ref)
        if not res:
            self.print_error("NET_DVR_ActivateDevice 激活设备失败: the error code is ")
        return res

    # msg 描述前缀
    def print_error(self, msg=""):
        error_info = self.call_cpp("NET_DVR_GetLastError")
        logging.error(msg + str(error_info))

    def get_device_configuration(self, user_id, channel=2):
        # Set up the output buffer size for the device configuration
        device_info = base.NET_DVR_DEVICECFG_V50()
        out_buffer_size = sizeof(base.NET_DVR_DEVICECFG_V50)
        # print(f"out_buffer_size: {out_buffer_size}")
        bytes_returned = c_ulong(0)
        NET_DVR_GET_DEVICECFG_V50=3801
        # Call the NET_DVR_GetDVRConfig function to get the device configuration
        # print(f"Retrieving device configuration for user_id {user_id} - channel {channel}...")
        result = self.call_cpp("NET_DVR_GetDVRConfig", user_id, NET_DVR_GET_DEVICECFG_V50, channel, byref(device_info), out_buffer_size, byref(bytes_returned))

        print(f"NET_DVR_GetDVRConfig result: {result}")
        if result == -1:
            self.print_error("NET_DVR_GetDVRConfig: the error code is ")
        else:
            print(f"Device configuration retrieved successfully. {result}")
            # You can print out or process the retrieved device information
            sSerialNumber = device_info.sSerialNumber
            # print("sSerialNumber:", bytes(sSerialNumber).decode('utf-8', errors='ignore').strip('\x00'))
            # sDVRName: <hkws.model.base.c_byte_Array_64 object at 0x000001736F54D5D0>
            sDVRName = device_info.sDVRName
            # print("sDVRName:", bytes(sDVRName).decode('utf-8', errors='ignore').strip('\x00'))
            # byDevTypeName: <hkws.model.base.c_byte_Array_32 object at 0x0000027662EAD5D0>
            byDevTypeName = device_info.byDevTypeName
            # print("byDevTypeName:", bytes(byDevTypeName).decode('utf-8', errors='ignore').strip('\x00'))
            # print all params of device_info
            # print(f'\n\n')
            for field_name, field_type in device_info._fields_:
                pass
                # print(f"{field_name}:", getattr(device_info, field_name))