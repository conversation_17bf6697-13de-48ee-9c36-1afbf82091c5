# 硬件产品基础结构体定义
# 包含初始化SDK,用户注册设备等相关结构体
from cam_scan.api.hikvision.hkws.core.const import *
from cam_scan.api.hikvision.hkws.core.type_map import *

NET_DVR_GET_NETCFG_V50 = 1015

# 设置sdk加载路劲
class NET_DVR_LOCAL_SDK_PATH(Structure):
    _fields_ = [("sPath", h_BYTE * 256), ("byRes", h_BYTE * 128)]


# 登录参数结构体
class NET_DVR_USER_LOGIN_INFO(Structure):
    _fields_ = [
        ("sDeviceAddress", h_BYTE * 129),  # 设备地址，IP或者普通域名
        ("byUseTransport", h_BYTE),  # 是否启用能力透传 0：不启动，默认  1：启动
        ("wPort", h_WORD),  # 设备端口号
        ("sUserName", h_BYTE * 64),  # 登录用户名
        ("sPassword", h_BYTE * 64),  # 登录密码
        # ("fLoginResultCallBack",)  #
        ("bUseAsynLogin", h_BOOL),  # 是否异步登录, 0:否 1:是
        ("byProxyType", h_BYTE),  # 代理服务器类型：0- 不使用代理，1- 使用标准代理，2- 使用EHome代理
        # 是否使用UTC时间：
        # 0 - 不进行转换，默认；
        # 1 - 输入输出UTC时间，SDK进行与设备时区的转换；
        # 2 - 输入输出平台本地时间，SDK进行与设备时区的转换
        ("byUseUTCTime", h_BYTE),
        # 登录模式(不同模式具体含义详见“Remarks”说明)：
        # 0- SDK私有协议，
        # 1- ISAPI协议，
        # 2- 自适应（设备支持协议类型未知时使用，一般不建议）
        ("byLoginMode", h_BYTE),
        # ISAPI协议登录时是否启用HTTPS(byLoginMode为1时有效)：
        # 0 - 不启用，
        # 1 - 启用，
        # 2 - 自适应（设备支持协议类型未知时使用，一般不建议）
        ("byHttps", h_BYTE),
        # 代理服务器序号，添加代理服务器信息时相对应的服务器数组下表值
        ("iProxyID", h_LONG),
        # 保留，置为0
        ("byRes3", h_BYTE * 120),
    ]


# 设备参数结构体。
class NET_DVR_DEVICEINFO_V30(Structure):
    _fields_ = [
        ("sSerialNumber", h_BYTE * 48),  # 序列号
        ("byAlarmInPortNum", h_BYTE),  # 模拟报警输入个数
        ("byAlarmOutPortNum", h_BYTE),  # 模拟报警输出个数
        ("byDiskNum", h_BYTE),  # 硬盘个数
        ("byDVRType", h_BYTE),  # 设备类型，详见下文列表
        ("byChanNum", h_BYTE),  # 设备模拟通道个数，数字(IP)通道最大个数为byIPChanNum + byHighDChanNum*256
        ("byStartChan", h_BYTE),  # 模拟通道的起始通道号，从1开始。数字通道的起始通道号见下面参数byStartDChan
        ("byAudioChanNum", h_BYTE),  # 设备语音对讲通道数
        ("byIPChanNum", h_BYTE),
        # 设备最大数字通道个数，低8位，搞8位见byHighDChanNum. 可以根据ip通道个数是否调用NET_DVR_GetDVRConfig (
        # 配置命令NET_DVR_GET_IPPARACFG_V40)获得模拟和数字通道的相关参数
        ("byZeroChanNum", h_BYTE),  # 零通道编码个数
        (
            "byMainProto",
            h_BYTE,
        ),  # 主码流传输协议类型： 0 - private, 1 - rtsp, 2- 同时支持私有协议和rtsp协议去留（默认采用私有协议取流）
        (
            "bySubProto",
            h_BYTE,
        ),  # 字码流传输协议类型： 0 - private , 1 - rtsp , 2 - 同时支持私有协议和rtsp协议取流 （默认采用私有协议取流）
        # 能力，位与结果为0表示不支持，1
        # 表示支持
        # bySupport & 0x1，表示是否支持智能搜索
        # bySupport & 0x2，表示是否支持备份
        # bySupport & 0x4，表示是否支持压缩参数能力获取
        # bySupport & 0x8, 表示是否支持双网卡
        # bySupport & 0x10, 表示支持远程SADP
        # bySupport & 0x20, 表示支持Raid卡功能
        # bySupport & 0x40, 表示支持IPSAN目录查找
        # bySupport & 0x80, 表示支持rtp over rtsp
        ("bySupport", h_BYTE),
        # 能力集扩充，位与结果为0表示不支持，1
        # 表示支持
        # bySupport1 & 0x1, 表示是否支持snmp
        # v30
        # bySupport1 & 0x2, 表示是否支持区分回放和下载
        # bySupport1 & 0x4, 表示是否支持布防优先级
        # bySupport1 & 0x8, 表示智能设备是否支持布防时间段扩展
        # bySupport1 & 0x10, 表示是否支持多磁盘数（超过33个）
        # bySupport1 & 0x20, 表示是否支持rtsp over http
        # bySupport1 & 0x80, 表示是否支持车牌新报警信息，且还表示是否支持NET_DVR_IPPARACFG_V40配置
        ("bySupport1", h_BYTE),
        # 能力集扩充，位与结果为0表示不支持，1
        # 表示支持
        # bySupport2 & 0x1, 表示解码器是否支持通过URL取流解码
        # bySupport2 & 0x2, 表示是否支持FTPV40
        # bySupport2 & 0x4, 表示是否支持ANR(断网录像)
        # bySupport2 & 0x20, 表示是否支持单独获取设备状态子项
        # bySupport2 & 0x40, 表示是否是码流加密设备
        ("bySupport2", h_BYTE),
        ("wDevType", h_WORD),  # 设备型号，详见下文列表
        # 能力集扩展，位与结果：0 - 不支持，1 - 支持
        # bySupport3 & 0x1, 表示是否支持多码流
        # bySupport3 & 0x4, 表示是否支持按组配置，具体包含通道图像参数、报警输入参数、IP报警输入 / 输出接入参数、用户参数、设备工作状态、JPEG抓图、定时和时间抓图、硬盘盘组管理等
        # bySupport3 & 0x20,表示是否支持通过DDNS域名解析取流
        ("bySupport3", h_BYTE),
        # 是否支持多码流，按位表示，位与结果：0 - 不支持，1 - 支持
        # byMultiStreamProto & 0x1, 表示是否支持码流3
        # byMultiStreamProto & 0x2, 表示是否支持码流4
        # byMultiStreamProto & 0x40, 表示是否支持主码流
        # byMultiStreamProto & 0x80, 表示是否支持子码流
        ("byMultiStreamProto", h_BYTE),
        ("byStartDChan", h_BYTE),  # 起始数字通道号，0表示无数字通道，比如DVR或IPC
        ("byStartDTalkChan", h_BYTE),  # 起始数字对讲通道号，区别于模拟对讲通道号，0表示无数字对讲通道
        ("byHighDChanNum", h_BYTE),  # 数字通道个数，高8位
        # 能力集扩展，按位表示，位与结果：0 - 不支持，1 - 支持
        # bySupport4 & 0x01, 表示是否所有码流类型同时支持RTSP和私有协议
        # bySupport4 & 0x10, 表示是否支持域名方式挂载网络硬盘
        ("bySupport4", h_BYTE),
        # 支持语种能力，按位表示，位与结果：0 - 不支持，1 - 支持
        # byLanguageType == 0，表示老设备，不支持该字段
        # byLanguageType & 0x1，表示是否支持中文
        # byLanguageType & 0x2，表示是否支持英文
        ("byLanguageType", h_BYTE),
        ("byVoiceInChanNum", h_BYTE),  # 音频输入通道数
        ("byStartVoiceInChanNo", h_BYTE),  # 音频输入起始通道号，0表示无效
        ("byRes3", h_BYTE * 2),  # 保留，置为0
        ("byMirrorChanNum", h_BYTE),  # 镜像通道个数，录播主机中用于表示导播通道
        ("wStartMirrorChanNo", h_WORD),  # 起始镜像通道号
        ("byRes2", h_BYTE * 2),
    ]  # 保留，置为0


class NET_DVR_DEVICEINFO_V40(Structure):
    _fields_ = [
        ("struDeviceV30", NET_DVR_DEVICEINFO_V30),  # 设备参数
        (
            "bySupportLock",
            h_BYTE,
        ),  # 设备是否支持锁定功能，bySuportLock 为1时，dwSurplusLockTime和byRetryLoginTime有效
        ("byRetryLoginTime", h_BYTE),  # 剩余可尝试登陆的次数，用户名，密码错误时，此参数有效
        # 密码安全等级： 0-无效，1-默认密码，2-有效密码，3-风险较高的密码，
        # 当管理员用户的密码为出厂默认密码（12345）或者风险较高的密码时，建议上层客户端提示用户名更改密码
        ("byPasswordLevel", h_BYTE),
        ("byProxyType", h_BYTE),  # 代理服务器类型，0-不使用代理，1-使用标准代理，2-使用EHome代理
        # 剩余时间，单位：秒，用户锁定时次参数有效。在锁定期间，用户尝试登陆，不算用户名密码输入对错
        # 设备锁定剩余时间重新恢复到30分钟
        ("dwSurplusLockTime", h_DWORD),
        # 字符编码类型（SDK所有接口返回的字符串编码类型，透传接口除外）：
        # 0 - 无字符编码信息（老设备）
        # 1 - GB2312
        ("byCharEncodeType", h_BYTE),
        # 支持v50版本的设备参数获取，设备名称和设备类型名称长度扩展为64字节
        ("bySupportDev5", h_BYTE),
        # 登录模式（不同的模式具体含义详见"Remarks"说明：0- SDK私有协议，1- ISAPI协议）
        ("byLoginMode", h_BYTE),
        # 保留，置为0
        ("byRes2", h_BYTE * 253),
    ]


class NET_DVR_Login_V40(Structure):
    _fields_ = [
        ("pLoginInfo", NET_DVR_USER_LOGIN_INFO),
        ("lpDeviceInfo", NET_DVR_DEVICEINFO_V40),
    ]


# 设备激活参数结构体
class NET_DVR_ACTIVATECFG(Structure):
    _fields_ = [
        ("dwSize", h_DWORD),
        ("sPassword", h_BYTE * PASSWD_LEN),
        ("byRes", h_BYTE * 108),
    ]


# SDK状态信息结构体
class NET_DVR_SDKSTATE(Structure):
    _fields_ = [
        ("dwTotalLoginNum", h_DWORD),  # 当前注册用户数
        ("dwTotalRealPlayNum", h_DWORD),  # 当前实时预览的路数
        ("dwTotalPlayBackNum", h_DWORD),  # 当前回放或下载的路数
        ("dwTotalAlarmChanNum", h_DWORD),  # 当前建立报警通道的路数
        ("dwTotalFormatNum", h_DWORD),  # 当前硬盘格式化的路数
        ("dwTotalFileSearchNum", h_DWORD),  # 当前文件搜索的路数
        ("dwTotalLogSearchNum", h_DWORD),  # 当前日志搜索的路数
        ("dwTotalSerialNum", h_DWORD),  # 当前建立透明通道的路数
        ("dwTotalUpgradeNum", h_DWORD),  # 当前升级的路数
        ("dwTotalVoiceComNum", h_DWORD),  # 当前语音转发的路数
        ("dwTotalBroadCastNum", h_DWORD),  # 当前语音广播的路数
        (" dwRes", h_DWORD * 10),  # 保留，置为0
    ]


# SDK功能信息结构体
class NET_DVR_SDKABL(Structure):
    _fields_ = [
        ("dwMaxLoginNum", h_DWORD),  # 最大注册用户数
        ("dwMaxRealPlayNum", h_DWORD),  # 最大实时预览的路数
        ("dwMaxPlayBackNum", h_DWORD),  # 最大回放或下载的路数
        ("dwMaxAlarmChanNum", h_DWORD),  # 最大建立报警通道的路数
        ("dwMaxFormatNum", h_DWORD),  # 最大硬盘格式化的路数
        ("dwMaxFileSearchNum", h_DWORD),  # 最大文件搜索的路数
        ("dwMaxLogSearchNum", h_DWORD),  # 最大日志搜索的路数
        ("dwMaxSerialNum", h_DWORD),  # 最大建立透明通道的路数
        ("dwMaxUpgradeNum", h_DWORD),  # 最大升级的路数
        ("dwMaxVoiceComNum", h_DWORD),  # 最大语音转发的路数
        ("dwMaxBroadCastNum", h_DWORD),  # 最大语音广播的路数
        (" dwRes", h_DWORD * 10),  # 保留，置为0
    ]

# Create the NET_DVR_DEVICECFG class as a ctypes Structure
# Define NET_DVR_DEVICECFG class (based on the provided C structure)
class NET_DVR_DEVICECFG(Structure):
    _fields_ = [
        ("dwSize", h_DWORD),  # Size of the structure
        ("sDVRName", c_char * 64),  # DVR Name (up to 64 bytes)
        ("dwDVRID", h_DWORD),  # DVR ID for IR Control
        ("dwRecycleRecord", h_DWORD),  # Cycle record, 0: No cycle, 1: Cycle
        ("sSerialNumber", c_char * 48),  # Serial Number (48 bytes)
        ("dwSoftwareVersion", h_DWORD),  # Software version
        ("dwSoftwareBuildDate", h_DWORD),  # Software build date (YYYYMMDD)
        ("dwDSPSoftwareVersion", h_DWORD),  # DSP software version
        ("dwDSPSoftwareBuildDate", h_DWORD),  # DSP software build date (YYYYMMDD)
        ("dwPanelVersion", h_DWORD),  # Front panel version
        ("dwHardwareVersion", h_DWORD),  # Hardware version
        ("byAlarmInPortNum", h_BYTE),  # Number of alarm input ports
        ("byAlarmOutPortNum", h_BYTE),  # Number of alarm output ports
        ("byRS232Num", h_BYTE),  # Number of RS232 ports
        ("byRS485Num", h_BYTE),  # Number of RS485 ports
        ("byNetworkPortNum", h_BYTE),  # Number of network ports
        ("byDiskCtrlNum", h_BYTE),  # Number of disk controllers
        ("byDiskNum", h_BYTE),  # Number of disks
        ("byDVRType", h_BYTE),  # DVR type
        ("byChanNum", h_BYTE),  # Channel number
        ("byStartChan", h_BYTE),  # Start channel number
        ("byDecordChans", h_BYTE),  # Number of decoding channels
        ("byVGANum", h_BYTE),  # Number of VGA ports
        ("byUSBNum", h_BYTE),  # Number of USB ports
        ("byAuxoutNum", h_BYTE),  # Number of AUX output ports
        ("byAudioNum", h_BYTE),  # Number of audio ports
        ("byIPChanNum", h_BYTE),  # Maximum number of IP channels
    ]

class NET_DVR_DEVICECFG_V50(Structure):
    _fields_ = [
        ("dwSize", h_DWORD),  # Size of the structure
        ("sDVRName", h_BYTE * 64),  # DVR name (NET_DEV_NAME_LEN)
        ("dwDVRID", h_DWORD),  # DVR ID (V1.4: 0-99, V1.5: 0-255)
        ("dwRecycleRecord", h_DWORD),  # Recycle record (0-disable, 1-enable)
        ("sSerialNumber", h_BYTE * 48),  # Serial number (SIMPLE Serial)
        ("dwSoftwareVersion", h_DWORD),  # Software version
        ("dwSoftwareBuildDate", h_DWORD),  # Build date (0xYYYYMMDD)
        ("dwDSPSoftwareVersion", h_DWORD),  # DSP version
        ("dwDSPSoftwareBuildDate", h_DWORD),  # DSP build date
        ("dwPanelVersion", h_DWORD),  # Front panel version
        ("dwHardwareVersion", h_DWORD),  # Hardware version
        ("byAlarmInPortNum", h_BYTE),  # Alarm input ports number
        ("byAlarmOutPortNum", h_BYTE),  # Alarm output ports number
        ("byRS232Num", h_BYTE),  # RS232 ports number
        ("byRS485Num", h_BYTE),  # RS485 ports number
        ("byNetworkPortNum", h_BYTE),  # Network port number
        ("byDiskCtrlNum", h_BYTE),  # HDD number for disk control
        ("byDiskNum", h_BYTE),  # Number of disks
        ("byDVRType", h_BYTE),  # DVR type (1: DVR, 2: ATM DVR, 3: DVS, ...)
        ("byChanNum", h_BYTE),  # Channel number
        ("byStartChan", h_BYTE),  # Start channel number (DVS: 1, DVR: 1)
        ("byDecordChans", h_BYTE),  # Decoding channels
        ("byVGANum", h_BYTE),  # VGA ports number
        ("byUSBNum", h_BYTE),  # USB ports number
        ("byAuxoutNum", h_BYTE),  # Auxiliary output number
        ("byAudioNum", h_BYTE),  # Audio interface number
        ("byIPChanNum", h_BYTE),  # Max IP channel number
        ("byZeroChanNum", h_BYTE),  # Zero channel number
        ("bySupport", h_BYTE),  # Ability set
        ("byEsataUseage", h_BYTE),  # E-SATA usage (0: backup, 1: record)
        ("byIPCPlug", h_BYTE),  # Enable plug-and-play for IPC (0: disable, 1: enable)
        ("byStorageMode", h_BYTE),  # Hard Disk Mode (0: group, 1: quota, 2: drawframe)
        ("bySupport1", h_BYTE),  # Additional abilities
        ("wDevType", h_WORD),  # Device type
        ("byDevTypeName", h_BYTE * 64),  # Device model name
        ("bySupport2", h_BYTE),  # Extended support abilities
        ("byAnalogAlarmInPortNum", h_BYTE),  # Analog alarm input number
        ("byStartAlarmInNo", h_BYTE),  # Start alarm input number
        ("byStartAlarmOutNo", h_BYTE),  # Start alarm output number
        ("byStartIPAlarmInNo", h_BYTE),  # Start IP alarm input number
        ("byStartIPAlarmOutNo", h_BYTE),  # Start IP alarm output number
        ("byHighIPChanNum", h_BYTE),  # High 8 bits of IP channel number
        ("byEnableRemotePowerOn", h_BYTE),  # Enable remote power-on (0: disable, 1: enable)
        ("byRes2", h_BYTE * 256)  # Reserved bytes
    ]

# Define constants
MAX_ETHERNET = 2  # Example, replace with actual value
MACADDR_LEN = 6
MAX_DOMAIN_NAME = 64
NAME_LEN = 32
PASSWD_LEN = 16

# Define the NET_DVR_IPADDR structure
class NET_DVR_IPADDR(Structure):
    _fields_ = [
        ("sIpV4", h_CHAR * 16),  # IPv4 Address
        ("byIPv6", h_BYTE * 128)  # Reserved for IPv6
    ]

# Define the NET_DVR_ETHERNET_V30 structure
class NET_DVR_ETHERNET_V30(Structure):
    _fields_ = [
        ("struDVRIP", NET_DVR_IPADDR),       # DVR IP address
        ("struDVRIPMask", NET_DVR_IPADDR),   # DVR IP Mask
        ("dwNetInterface", h_DWORD),         # Network port type
        ("wDVRPort", h_WORD),                # Port
        ("wMTU", h_WORD),                    # MTU, default 1500
        ("byMACAddr", h_BYTE * MACADDR_LEN), # MAC address
        ("byEthernetPortNo", h_BYTE),        # Ethernet port number
        ("byRes", h_BYTE * 1)                # Reserved
    ]

# Define the NET_DVR_PPPOECFG structure
class NET_DVR_PPPOECFG(Structure):
    _fields_ = [
        ("dwPPPOE", h_DWORD),                         # 0- disable, 1- enable
        ("sPPPoEUser", h_CHAR * NAME_LEN),            # PPPoE user name
        ("sPPPoEPassword", h_CHAR * PASSWD_LEN),      # PPPoE password
        ("struPPPoEIP", NET_DVR_IPADDR)               # PPPoE IP address
    ]

# Define the NET_DVR_NETCFG_V50 structure
class NET_DVR_NETCFG_V50(Structure):
    _fields_ = [
        ("dwSize", h_DWORD),
        ("struEtherNet", NET_DVR_ETHERNET_V30 * MAX_ETHERNET),  # Network ports array
        ("struRes1", NET_DVR_IPADDR * 2),                       # Reserved IP addresses
        ("struAlarmHostIpAddr", NET_DVR_IPADDR),                # Alarm Host IP address
        ("byRes2", h_BYTE * 4),                                 # Reserved
        ("wAlarmHostIpPort", h_WORD),                           # Alarm Host Port
        ("byUseDhcp", h_BYTE),                                  # DHCP enable flag
        ("byIPv6Mode", h_BYTE),                                 # IPv6 allocation mode
        ("struDnsServer1IpAddr", NET_DVR_IPADDR),               # DNS server 1 IP address
        ("struDnsServer2IpAddr", NET_DVR_IPADDR),               # DNS server 2 IP address
        ("byIpResolver", h_CHAR * MAX_DOMAIN_NAME),             # IP resolver domain or IP address
        ("wIpResolverPort", h_WORD),                            # IP resolver port
        ("wHttpPortNo", h_WORD),                                # HTTP port number
        ("struMulticastIpAddr", NET_DVR_IPADDR),                # Multicast IP address
        ("struGatewayIpAddr", NET_DVR_IPADDR),                  # Gateway IP address
        ("struPPPoE", NET_DVR_PPPOECFG),                        # PPPoE configuration
        ("byEnablePrivateMulticastDiscovery", h_BYTE),          # Private multicast discovery flag
        ("byEnableOnvifMulticastDiscovery", h_BYTE),            # ONVIF multicast discovery flag
        ("wAlarmHost2IpPort", h_WORD),                          # Alarm host 2 port
        ("struAlarmHost2IpAddr", NET_DVR_IPADDR),               # Alarm host 2 IP address
        ("byEnableDNS", h_BYTE),                                # DNS enable flag
        ("byRes", h_BYTE * 599)                                 # Reserved bytes
    ]

# Define the NET_DVR_NETCFG_V30 structure
class NET_DVR_NETCFG_V30(Structure):
    _fields_ = [
        ("dwSize", h_DWORD),
        ("struEtherNet", NET_DVR_ETHERNET_V30 * MAX_ETHERNET),  # Network ports array
        ("struRes1", NET_DVR_IPADDR * 2),                       # Reserved IP addresses
        ("struAlarmHostIpAddr", NET_DVR_IPADDR),                # Alarm Host IP address
        ("byRes2", h_BYTE * 4),                                 # Reserved
        ("wAlarmHostIpPort", h_WORD),                           # Alarm Host Port
        ("byUseDhcp", h_BYTE),                                  # DHCP enable flag
        ("byIPv6Mode", h_BYTE),                                 # IPv6 allocation mode
        ("struDnsServer1IpAddr", NET_DVR_IPADDR),               # DNS server 1 IP address
        ("struDnsServer2IpAddr", NET_DVR_IPADDR),               # DNS server 2 IP address
        ("byIpResolver", h_CHAR * MAX_DOMAIN_NAME),             # IP resolver domain or IP address
        ("wIpResolverPort", h_WORD),                            # IP resolver port
        ("wHttpPortNo", h_WORD),                                # HTTP port number
        ("struMulticastIpAddr", NET_DVR_IPADDR),                # Multicast IP address
        ("struGatewayIpAddr", NET_DVR_IPADDR),                  # Gateway IP address
        ("struPPPoE", NET_DVR_PPPOECFG),                        # PPPoE configuration
        ("byEnablePrivateMulticastDiscovery", h_BYTE),          # Private multicast discovery flag
        ("byEnableOnvifMulticastDiscovery", h_BYTE),            # ONVIF multicast discovery flag
        ("byEnableDNS", h_BYTE),                                # DNS enable flag
        ("byRes", h_BYTE * 61)                                  # Reserved bytes
    ]


# Define NET_DVR_IPADDR_UNION (assuming it's a union for IP address)
class NET_DVR_IPADDR_UNION(Structure):
    _fields_ = [
        ("sIpV4", h_CHAR * 16),   # IPv4 Address (16 bytes to fit the max length for IPs)
        ("byIPv6", h_BYTE * 128)  # IPv6 address (128 bytes)
    ]

# Define the NET_DVR_LINK_ADDR structure
class NET_DVR_LINK_ADDR(Structure):
    _fields_ = [
        ("uLocalIP", NET_DVR_IPADDR_UNION),   # Local IP address
        ("wLocalPort", h_WORD * 10),          # Local port (array of 10 ports)
        ("byLocalPortNum", h_BYTE),           # Number of local ports
        ("byRes1", h_BYTE * 3),               # Reserved bytes
        ("uDevIP", NET_DVR_IPADDR_UNION),     # Device IP address
        ("wDevPort", h_WORD * 10),            # Device ports (array of 10 ports)
        ("byDevPortNum", h_BYTE),             # Number of device ports
        ("byRes2", h_BYTE * 3),               # Reserved bytes
        ("byRes", h_BYTE * 80)                # Additional reserved space (80 bytes)
    ]

from enum import Enum

# Define the NET_DVR_LINK_KIND enum
class NET_DVR_LINK_KIND(Enum):
    ENUM_LINK_PREVIEW = 1  # Preview
    ENUM_LINK_PLAYBACK = 2  # Playback, download
    ENUM_LINK_VOICEPLAY = 3  # Audio talk