import configparser

from cam_scan.api.hikvision.hkws.core import env


class Config:
    SDKPath = "D:\\WORK\\hikvision_dll_binding\\sdk\\lib\\"
    User = "admin"
    Password = "abcd1234"
    Port = 8000
    IP = "**************"
    Suffix = ".so"

    def InitConfig(self, path):
        cnf = configparser.ConfigParser()
        cnf.read(path)
        print(cnf)
        self.SDKPath = cnf.get("DEFAULT", "SDKPath")
        self.User = cnf.get("DEFAULT", "User")
        self.Password = cnf.get("DEFAULT", "Password")
        self.Port = cnf.getint("DEFAULT", "Port")
        self.IP = cnf.get("DEFAULT", "IP")
        if env.isWindows():
            self.Suffix = ".dll"
        return
