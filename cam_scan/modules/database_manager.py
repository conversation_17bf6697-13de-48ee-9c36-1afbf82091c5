import sqlite3
import ipaddress
class Singleton(type):
    _instances = {}

    def __call__(cls, *args, **kwargs):
        if cls not in cls._instances:
            instance = super().__call__(*args, **kwargs)
            cls._instances[cls] = instance
        return cls._instances[cls]

class DatabaseManager(metaclass=Singleton):
    def __init__(self):
        self.db = None
        self.list_keys = {}
        self.init_database()

    def init_database(self):    
        self.db = sqlite3.connect('temp.db') # Open a database File
        self.db.execute(''' CREATE TABLE IF NOT EXISTS ip_list(
            ID FLOAT PRIMARY KEY NOT NULL,
            DATA TEXT NOT NULL) ''')
        self.cursor = self.db.cursor()

    def add_database(self,ip_address):
        first_octet = self.get_first_octet(ip_address)
        data = (first_octet, ip_address)
        if first_octet in self.list_keys:
            
            if str(ip_address) in str(self.list_keys[first_octet]):
                # print(f'aaaaaaaa = {first_octet}')
                return False
        else:
            record = self.read_data(first_octet)
            if record is not None:
                if str(ip_address) in str(record):
                    return False
                else:
                    self.cursor.execute("INSERT INTO ip_list (ID, DATA) VALUES (?, ?) ON CONFLICT(ID) DO UPDATE SET DATA = DATA || ', ' || excluded.DATA", data)
                    self.db.commit()
                        # return ok
                    return True
            else:
                self.cursor.execute("INSERT INTO ip_list (ID, DATA) VALUES (?, ?) ON CONFLICT(ID) DO UPDATE SET DATA = DATA || ', ' || excluded.DATA", data)
                self.db.commit()
                    # return ok
                return True
        # ok = self.check_data_exist(ip_address)
        # if ok:
            # # Sử dụng câu lệnh INSERT INTO với ON CONFLICT để cập nhật dữ liệu khi ID đã tồn tại
        
        self.cursor.execute("INSERT INTO ip_list (ID, DATA) VALUES (?, ?) ON CONFLICT(ID) DO UPDATE SET DATA = DATA || ', ' || excluded.DATA", data)
        self.db.commit()
            # return ok
        return True
    
    def read_data(self,id):
        # from math import *
        # data = self.db.execute(''' SELECT * FROM ip_list ORDER BY NAME''')
        self.cursor.execute("SELECT * FROM ip_list WHERE id = ?", (id,))
        record = self.cursor.fetchone()
        if record:
            self.list_keys[id] = record[1]
            # print(f'self.list_keys[id] = {type(self.list_keys[id])}')
            return record
        return None
    
    def delete_data(self):
#         self.cursor.execute("DELETE FROM ip_list")
#         self.db.commit()

# # Kiểm tra xem bảng có tồn tại không
        self.cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='ip_list'")
        table_exists = self.cursor.fetchone()

        if table_exists:
            # Bảng tồn tại, thực hiện xóa hết dữ liệu
            self.cursor.execute("DELETE FROM ip_list")
            self.db.commit()

    def check_data_exist(self,ip_address):
        self.cursor.execute("SELECT COUNT(*) FROM ip_list WHERE DATA LIKE ?", (f'%{ip_address}%',))
        count = self.cursor.fetchone()[0]
        if count > 0:
            return False
        else:
            return True
        
    def get_first_octet(self,ip_address):
        ip_parts = ip_address.split('.')
        first_two_numbers = ip_parts[:2]
        result = '.'.join(first_two_numbers)
        
        return result 
database = DatabaseManager()

# database.add_database('*******')