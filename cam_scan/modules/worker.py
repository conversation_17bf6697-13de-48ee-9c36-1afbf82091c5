from queue import Queue
from threading import RLock
from typing import List,Callable
#from rich.progress import TaskID
import logging
from cam_scan.modules.attack import attack_credentials, attack_route, attack_onvif, get_screenshot,attack_find_port,attack_api
#from src.common.rtspbrute.modules.cli.output import ProgressBar
from cam_scan.modules.cam_info_client import CamInfoClient
from cam_scan.modules.utils import start_threads,wait_for
from onvif import ONVIFCamera, exceptions
from cam_scan.modules.config import config,ScanState
from zeep.transports import Transport
# from cam_scan.modules.database_manager import database
import time
import json
# PROGRESS_BAR: ProgressBar
# CHECK_PROGRESS: TaskID
# BRUTE_PROGRESS: TaskID
# SCREENSHOT_PROGRESS: TaskID
LOCK = RLock()
logger = logging.getLogger(__name__)

def find_port(input_queue: Queue, output_queue: Queue, result_queue: Queue) -> None:
    while True:
        target: CamInfoClient = input_queue.get()
        if target is None:
            break

        result = attack_find_port(target)
        
        if result:
            output_queue.put(target)
        else:
            output_queue.put(target)
        input_queue.task_done()

def brute_routes(input_queue: Queue, output_queue: Queue,result_queue: Queue) -> None:
    while True:
        if config.state == ScanState.Stop:
            # target: CamInfoClient = input_queue.get()
            # if target is None:
            #     break
            # target: CamInfoClient = input_queue.get()
            # input_queue.get_nowait()
            try:
                input_queue.get_nowait()
            except Exception as e:
                break
            input_queue.task_done()
        else:
            if config.state == ScanState.Play:
                target: CamInfoClient = input_queue.get()
                if target is None:
                    break
                # logger.info(f'brute_routes target = {target.ip}')
                result = attack_route(target)
                # logger.info(f'brute_routes1 target = {result}')
                if result:
                    while True:
                        if not output_queue.full():
                            output_queue.put(target)
                            break
                else:
                    target.error_info = "brute_routes fail"
                    result_queue.put(target)

            
                input_queue.task_done()

def brute_api(input_queue: Queue, output_queue: Queue,result_queue: Queue) -> None:
    while True:
        
        if config.state == ScanState.Stop:
            # target: CamInfoClient = input_queue.get()
            # if target is None:
            #     break
            # input_queue.task_done()
            try:
                input_queue.get_nowait()
            except Exception as e:
                break
            input_queue.task_done()
        else:
            if config.state == ScanState.Play:
                target: CamInfoClient = input_queue.get()
                if target is None:
                    break
                # logger.info(f'brute_api')
                result = attack_api(target)
                if result:
                    while True:
                        if not output_queue.full():
                            output_queue.put(target)
                            break
                else:
                    target.error_info = "brute_api fail"
                    result_queue.put(target)
                input_queue.task_done()
    
def brute_credentials(input_queue: Queue, output_queue: Queue, result_queue: Queue) -> None:
    while True:
        # logger.info(f'brute_credentials = {state}')
        if config.state == ScanState.Stop:
            # target: CamInfoClient = input_queue.get()
            # if target is None:
            #     break
            # input_queue.task_done()
            try:
                input_queue.get_nowait()
            except Exception as e:
                break
            input_queue.task_done()
        else:
            if config.state == ScanState.Play:
                target: CamInfoClient = input_queue.get()
                if target is None:
                    break
                target.has_camera = True
                # logger.info(f'brute_credentials target = {target}')
                result = attack_credentials(target)
                
                if result:
                    while True:
                        if not output_queue.full():
                            output_queue.put(target)
                            break
                else:
                    target.error_info = "brute_credentials fail"
                    result_queue.put(target)
                input_queue.task_done()

def brute_onvif(input_queue: Queue, output_queue: Queue,result_queue: Queue) -> None:
    while True:
        if config.state == ScanState.Stop:
            # target: CamInfoClient = input_queue.get()
            # if target is None:
            #     break
            # input_queue.task_done()
            try:
                input_queue.get_nowait()
            except Exception as e:
                break
            input_queue.task_done()
        else:
            if config.state == ScanState.Play:
                target_url: CamInfoClient = input_queue.get()
                # logger.info(f"brute_onvif target_url = {target_url}")
                if target_url is None:
                    break
                # logger.info(f'brute_onvif target = {target_url}')
                result = attack_onvif(target_url.ip)
                if not result:
                    while True:
                        if not output_queue.full():
                            output_queue.put(target_url)
                            break
                else:
                    try:
                        if ':' in target_url.credentials:
                            username, password = target_url.credentials.split(":")
                            # # Tạo đối tượng ONVIFCamera với tên người dùng và mật khẩu
                            camera = ONVIFCamera(target_url.ip, 80, username, password, transport=Transport(operation_timeout=3))
                            # Lấy thông tin thiết bị
                            device_info = camera.devicemgmt.GetDeviceInformation()
                            # Lấy thông tin mô tả
                            manufacturer = device_info.Manufacturer
                            model = device_info.Model
                            # logger.info(f'target_url.brand = {target_url.brand}')
                            if target_url.brand is None:
                                target_url.brand = manufacturer   
                            target_url.model = model
                            # firmware_version = device_info.FirmwareVersion
                            temp_data = get_list_rtsp(onvif_camera = camera,username=username, password=password,cam_info = target_url)
                            if temp_data is not None:
                                target_url.number_cameras = len(temp_data)
                                # nếu có thông tin onvif thì clear thông tin cam_cfg (có thể chạy qua Dahua API đã có data rồi)
                                if target_url.number_cameras > 0:
                                    target_url.cam_cfg = []
                                for key,data in temp_data.items():
                                    target_url.cam_cfg.append(data)
                                    # data.update({'username': username, 'password': password, 'ip': target_url.ip, 'port': 80,'manufacturer': manufacturer,'camera_type':'Onvif','camera_model':model})

                                    # rtsp_output.append(data)
                                target_url.state = True
                                result_queue.put(target_url)
                                # if callback_result is not None:
                                #     callback_result(target_url.output())
                            else:
                                while True:
                                    if not output_queue.full():
                                        output_queue.put(target_url)
                                        break
                    except exceptions.ONVIFError as e:
                        # Không thành công kết nối hoặc không hỗ trợ ONVIF
                        print(f"ONVIFError = {e}")
                        while True:
                            if not output_queue.full():
                                output_queue.put(target_url)
                                break
                    
                input_queue.task_done()

def screenshot_targets(input_queue: Queue,result_queue: Queue) -> None:
    while True:
        if config.state == ScanState.Stop:
            # target: CamInfoClient = input_queue.get()
            # if target is None:
            #     break
            # input_queue.task_done()
            try:
                input_queue.get_nowait()
            except Exception as e:
                break
            input_queue.task_done()
        else:
            if config.state == ScanState.Play:
                target_url: CamInfoClient = input_queue.get()
                if target_url is None:
                    break
                # logger.info(f'target_url = {target_url}')
                if len(target_url.cam_cfg) > 0:
                    target_url.state = True
                    result_queue.put(target_url)
                    input_queue.task_done()
                    continue

                try:
                    camera = {}
                    # camera['username'],camera['password'] = target_url.credentials.split(':')
                    # camera['ip'] = target_url.ip
                    # camera['port'] = target_url.port
                    camera['profileToken'] = None
                    camera['manufacturer'] = None
                    camera['is_ptz'] = None
                    camera['camera_type'] = None
                    camera['camera_model'] = None
                    target_url.user,target_url.password = target_url.credentials.split(':')
                    str_target_url = str(target_url)
                    # check if "/Streaming/Channels/101/" is in target_url
                    if "/Streaming/Channels/101/" in str_target_url:
                        target_url.brand = "Hikvision"
                        # for i in range(1, 17):
                        #     main_target_url = str_target_url.replace("/Streaming/Channels/101/", f"/Streaming/Channels/{i}01/")
                        #     image = get_screenshot(main_target_url)
                        #     if image:
                        #         #with LOCK:
                        #         copied_camera = camera.copy()
                        #         copied_camera['rtsp_list'] = [main_target_url]
                        #         # rtsp_list.append(copied_camera)
                        #         sub_target_url = str_target_url.replace(f"/Streaming/Channels/101/", f"/Streaming/Channels/{i}02/")
                        #         image = get_screenshot(sub_target_url)
                        #         if image:
                        #             copied_camera['rtsp_list'].append(sub_target_url)
                        #         target_url.cam_cfg.append(copied_camera)
                        # target_url.number_cameras = len(target_url.cam_cfg)
                        # # if callback_result is not None:
                        # #     callback_result(target_url.output())
                        # if len(target_url.cam_cfg) > 0:
                        #     target_url.state = True
                        # result_queue.put(target_url)
                        def get_frame(queue:Queue):
                            while True:
                                url: str = queue.get()
                                if url is None:
                                    break
                                main_target_url,sub_target_url = url
                                image = get_screenshot(main_target_url)
                                if image:
                                    #with LOCK:
                                    copied_camera = camera.copy()
                                    copied_camera['rtsp_list'] = [main_target_url]
                                    image = get_screenshot(sub_target_url)
                                    if image:
                                        copied_camera['rtsp_list'].append(sub_target_url)
                                    target_url.cam_cfg.append(copied_camera)
                                queue.task_done() 

                        queue = Queue()
                        screenshot_threads = start_threads(17,get_frame,queue)
                        for i in range(1, 17):
                            main_target_url = str_target_url.replace("/Streaming/Channels/101/", f"/Streaming/Channels/{i}01/")
                            sub_target_url = str_target_url.replace(f"/Streaming/Channels/101/", f"/Streaming/Channels/{i}02/")
                            queue.put((main_target_url,sub_target_url))
                        wait_for(queue,screenshot_threads)
                        target_url.number_cameras = len(target_url.cam_cfg)
                        if len(target_url.cam_cfg) > 0:
                            target_url.state = True
                        result_queue.put(target_url)
                    else:
                        # check if target_url doesn't have anything after :554/
                        if str_target_url.endswith(':554/'):
                            # ok = try_routes(target_url,camera)
                            ok = False
                            if ok:

                                result_queue.put(target_url)
                            else:
                                image = get_screenshot(str_target_url)
                                if image:
                                    #with LOCK:
                                    camera['rtsp_list'] = [str_target_url]
                                    sub_route = target_url.route.get(2,None)
                                    if sub_route is not None:
                                        sub_target_url = target_url.get_rtsp_url(target_url.ip, target_url.port, target_url.credentials, sub_route)
                                        image = get_screenshot(str(sub_target_url))
                                        if image:
                                            camera['rtsp_list'].append(str(sub_target_url))
                                            target_url.cam_cfg.append(camera)
                                    else:
                                        target_url.cam_cfg.append(camera)
                                    # if callback_result is not None:
                                    #     callback_result(target_url.output())
                                    target_url.state = True
                                    result_queue.put(target_url)
                                else:
                                    target_url.error_info = "get_screenshot fail"
                                    result_queue.put(target_url)
                        else:
                            image = get_screenshot(str_target_url)
                            print(f'str_target_url: {str_target_url} - {target_url.route.get(1,None)}')
                            if "cam/realmonitor/" in str_target_url:
                                target_url.brand = "Dahua"
                            elif "/axis-media/media.amp" in str_target_url:
                                target_url.brand = "Axis"
                            if image:
                                #with LOCK:
                                camera['rtsp_list'] = [str_target_url]
                                sub_route = target_url.route.get(2,None)
                                if sub_route is not None:
                                    sub_target_url = target_url.get_rtsp_url(target_url.ip, target_url.port, target_url.credentials, sub_route)
                                    image = get_screenshot(str(sub_target_url))
                                    if image:
                                        camera['rtsp_list'].append(str(sub_target_url))
                                        target_url.cam_cfg.append(camera)
                                    else:
                                        target_url.cam_cfg.append(camera)
                                else:
                                    target_url.cam_cfg.append(camera)
                                # if callback_result is not None:
                                #     callback_result(target_url.output())
                                target_url.state = True
                                result_queue.put(target_url)
                            else:
                                target_url.error_info = "get_screenshot fail"
                                result_queue.put(target_url)
                except Exception as e:
                    logger.info(f'screenshot_targets error: {e}')
                input_queue.task_done()

def try_routes(target_url: CamInfoClient,camera:dict):
                            # first try the unmodified URL
    # str_target_url = str(target_url)
    # target_url.routes.append({1:"/cam/realmonitor?channel=1&subtype=0",2:"/cam/realmonitor?channel=1&subtype=2"})
    # logger.info(f'try_routes = {target_url,target_url.routes}')
    for endpoint in config.additional_list:
        main_route = endpoint.get(1,None)
        main_target_url = target_url.get_rtsp_url(target_url.ip, target_url.port, target_url.credentials, main_route)
        image = get_screenshot(main_target_url)
        if image:
            camera['rtsp_list'] = [main_target_url]
            sub_route = endpoint.get(2,None)
            if sub_route is not None:
                sub_target_url = target_url.get_rtsp_url(target_url.ip, target_url.port, target_url.credentials, sub_route)
                image = get_screenshot(str(sub_target_url))
                if image:
                    camera['rtsp_list'].append(str(sub_target_url))
                    target_url.cam_cfg.append(camera)
                else:
                    target_url.cam_cfg.append(camera)
            else:
                target_url.cam_cfg.append(camera)  
            target_url.state = True
            return True
        else:
            pass
    return False

def export_result(input_queue: Queue) -> None:
    count = 0
    while True:
        if config.state == ScanState.Stop:
            target: CamInfoClient = input_queue.get()
            if target is None:
                break
            input_queue.task_done()
            # try:
            #     input_queue.get_nowait()
            # except Exception as e:
            #     break
            # input_queue.task_done()
        else:
            if config.state == ScanState.Play:
                target: CamInfoClient = input_queue.get()
                if target is None:
                    break
                count += 1
                target.no = count
                if config.callback_result is not None:
                    logger.info(f'Successfully scanned {target.no}/{target.total} IP {target.ip}')
                    config.callback_result(target.output())
                input_queue.task_done()

def check_ptz(camera:ONVIFCamera = None):
    # media_service = camera.create_media_service()
    # profiles = media_service.GetProfiles()
    # for profile in profiles:
    #     if profile['PTZConfiguration']['PanTiltLimits'] is not None:
    #         return True
    # return None
    try:
        ptz = camera.create_ptz_service()
        ptz_node = ptz.GetNodes()
        for item in ptz_node:
            if item['SupportedPTZSpaces'] and item['SupportedPTZSpaces']['AbsolutePanTiltPositionSpace']:
                return True
    except Exception as e:
        return None
def get_list_rtsp(onvif_camera:ONVIFCamera = None, username = None, password = None,cam_info:CamInfoClient = None):
    cameras = {}
    try:
        media_service = onvif_camera.create_media_service()
        profiles = media_service.GetProfiles()
        if len(profiles) != 0:
            try:
                video_encoder_configurations = media_service.GetVideoEncoderConfigurations()
            except Exception as e:
                video_encoder_configurations = None
                return None
            for index,profile in enumerate(profiles):
                # print(f'profile = {profile}')
                # break
                video_source_configuration_token = profile.VideoSourceConfiguration.token
                if video_source_configuration_token not in cameras:
                    camera = {'profileToken':profile.token}
                    if len(video_encoder_configurations) != 0:
                        target_encoder_configuration = video_encoder_configurations[index]
                        if target_encoder_configuration.Encoding == "H264":
                            # Lấy các tùy chọn cấu hình encoder
                            options = media_service.GetVideoEncoderConfigurationOptions({'ConfigurationToken': target_encoder_configuration.token})
                            resolutions = []
                            for item in options.H264.ResolutionsAvailable:
                                resolution = {'width': item.Width,'height': item.Height}
                                resolutions.append(resolution)
                            camera['supportedMainResolution'] = json.dumps(resolutions)
                            camera['mainstreamResolution'] = str(target_encoder_configuration.Resolution.Width) + 'x' + str(target_encoder_configuration.Resolution.Height)

                            fps = []
                            if options.H264.FrameRateRange != None:
                                fps_min = options.H264.FrameRateRange['Min']
                                fps_max = options.H264.FrameRateRange['Max']
                                for i in range(fps_min, fps_max + 1):
                                    fps.append(i)
                                camera['supportedMainFps'] = json.dumps(fps)
                            camera['mainstreamFps'] = json.dumps(target_encoder_configuration.RateControl.FrameRateLimit)
                    stream_setup = {
                        'StreamSetup': {
                            'Stream': 'RTP-Unicast',
                            'Transport': {
                                'Protocol': 'RTSP'
                            }
                        },
                        'ProfileToken': profile.token
                    }
                    stream_uri = media_service.GetStreamUri(stream_setup)
                    rtsp = stream_uri.Uri
                    rtsp = rtsp.replace("rtsp://", f"rtsp://{username}:{password}@")
                    camera['rtsp_list'] = [rtsp]

                    if profile.PTZConfiguration is not None:
                        if profile.PTZConfiguration.DefaultPTZSpeed is not None or profile.PTZConfiguration.PanTiltLimits is not None or profile.PTZConfiguration.ZoomLimits is not None:
                            camera['is_ptz'] = True
                        else:
                            camera['is_ptz'] = None
                    else:
                        camera['is_ptz'] = None
                    # is_continuous_move = check_continuous_move(camera = onvif_camera,profileToken=profile.token)
                    # camera['is_continuous_move'] = is_continuous_move
                    cameras[video_source_configuration_token] = camera
                else:
                    try:
                        camera = cameras[video_source_configuration_token]
                        # logger.info(f"ahihi = {len(video_encoder_configurations),index}")
                            # chi get them 1 luong substream thoi
                        if len(video_encoder_configurations) != 0:
                            target_encoder_configuration = video_encoder_configurations[index]
                            if target_encoder_configuration.Encoding == "H264":
                                # Lấy các tùy chọn cấu hình encoder
                                options = media_service.GetVideoEncoderConfigurationOptions({'ConfigurationToken': target_encoder_configuration.token})
                                resolutions = []
                                for item in options.H264.ResolutionsAvailable:
                                    resolution = {'width': item.Width,'height': item.Height}
                                    resolutions.append(resolution)
                                camera['supportedSubResolution'] = json.dumps(resolutions)
                                camera['substreamResolution'] = str(target_encoder_configuration.Resolution.Width) + 'x' + str(target_encoder_configuration.Resolution.Height)
                                fps = []
                                if options.H264.FrameRateRange != None:
                                    fps_min = options.H264.FrameRateRange['Min']
                                    fps_max = options.H264.FrameRateRange['Max']
                                    for i in range(fps_min, fps_max + 1):
                                        fps.append(i)
                                    camera['supportedSubFps'] = json.dumps(fps)
                                camera['substreamFps'] = json.dumps(target_encoder_configuration.RateControl.FrameRateLimit)
                                
                        if len(camera['rtsp_list']) == 1:
                            stream_setup = {
                                'StreamSetup': {
                                    'Stream': 'RTP-Unicast',
                                    'Transport': {
                                        'Protocol': 'RTSP'
                                    }
                                },
                                'ProfileToken': profile.token
                            }
                            stream_uri = media_service.GetStreamUri(stream_setup)
                            rtsp = stream_uri.Uri
                            rtsp = rtsp.replace("rtsp://", f"rtsp://{username}:{password}@")
                            camera['rtsp_list'].append(rtsp)
                    except Exception as e:
                        logging.info(f'error: {e}')
        return cameras
    except Exception as e:
        logger.info(f'error get_list_rtsp {e}')
    return None

def check_continuous_move(camera: ONVIFCamera= None,profileToken = None):
    try:
        ptz = camera.create_ptz_service()
        ptz_request = {}
        ptz_request["ProfileToken"] = profileToken
        ptz_request['Velocity'] = {}
        ptz_request['Velocity']['PanTilt'] = {}
        ptz_request['Velocity']['PanTilt']['x'] = 0.1
        ptz_request['Velocity']['PanTilt']['y'] = 0.1
        ptz_request['Velocity']['PanTilt']['space'] = 'http://www.onvif.org/ver10/tptz/PanTiltSpaces/VelocityGenericSpace'
        ptz_request['Velocity']['Zoom'] = None
        ptz_request['Timeout'] = None
        # logger.info(f'control_ptz = {ptz_request}')
        a = ptz.ContinuousMove(ptz_request)
        # if name_move == 'around':
        #     sleep(2)
        # else:
        time.sleep(0.02)
        ptz.Stop({'ProfileToken': profileToken})
        return True
    except Exception as e:
        print(f'checkout_continuous_move {e}')
        return None
