from dataclasses import dataclass, asdict

@dataclass
class CamInfo:
    ip: str = "Unknown"
    user: str = "Unknown"
    password: str = "Unknown"
    endpoint: str = "Unknown"
    port: str = "Unknown"
    brand: str = "Unknown"
    model: str = "Unknown"
    mac_address: str = "Unknown"
    serial_number: str = "Unknown"
    nvr: bool = False
    number_cameras: str = "Unknown"
    state: bool = False

    def __repr__(self):
        """In ra thông tin của Camera dưới dạng dict khi gọi print"""
        return str(asdict(self))

    def update_field(self, field_name: str, value):
        """Cập nhật một trường cụ thể"""
        if hasattr(self, field_name):
            setattr(self, field_name, value)
        else:
            raise AttributeError(f"'{field_name}' is not a valid field")

    def update_fields(self, **kwargs):
        """Cậ<PERSON> nhật nhiề<PERSON> tr<PERSON>ờng cùng lúc"""
        for field_name, value in kwargs.items():
            if hasattr(self, field_name):
                setattr(self, field_name, value)
            else:
                raise AttributeError(f"'{field_name}' is not a valid field")

# # Sử dụng thử
# camera = CameraInfo(
#     ip="************",
#     user="admin",
#     password="password123",
#     endpoint="http://************/stream",
#     port="8080",
#     brand="BrandX",
#     model="ModelY",
#     mac_address="00:1B:44:11:3A:B7",
#     serial_number="SN123456",
#     nvr=True,
#     number_cameras="4"
# )

# # Print thông tin dưới dạng dict
# print(camera)

# # Cập nhật một trường
# camera.update_field("user", "new_admin")

# # Cập nhật nhiều trường cùng lúc
# camera.update_fields(password="new_password", ip="************")

# # Print lại để kiểm tra thay đổi
# print(camera)