# from cam_scan.modules import utils

credential_list = [
    "admin:abcd1234",
    "admin:12345",
    "default:tluafed",
    "user:user",
    "admin:123456",
    "admin:hd543211",
    "admin:00000000",
    "admin:1111",
    "admin:admin",
    "admin:4321",
    "admin:1111111",
    "admin:123123",
    "admin:qwerty",
    "admin:88888888",
    "admin:99999999",
    "admin:admin123",
    "admin:",
    "admin:12888",
    "Admin:1234",
    "admin:9999",
    "Admin:123456",
    "administrator:",
    "admin:password",
    "root:root",
    "root:admin",
    "root:12345",
    "admin:admin1234",
    "admin:pass",
    "admin:1234",
    "admin:123456",
    "admin:123456789a",
    "admin:12345678",
    "admin:0000",
    "root:password",
    "user:password",
    "guest:guest",
    "admin:123",
    "admin:321",
    "admin:888888",
    "admin:666666",
    "admin:default",
    "admin:root",
    "user:admin",
    "guest:12345",
    "guest:123456",
    "admin:flir",
    "admin:system",
    "admin:netcam",
    "888888:888888",
    "service:service",
    "administrator:1234",
    "supervisor:supervisor",
    "operator:operator",
    "root:123456",
    "admin:admin12345",
    "admin:54321",
    "admin:pass1234",
    "admin:letmein",
    "admin:password1",
    "admin:password123",
    "user:1234",
    "user:123456",
    "admin:changeme",
    "admin:12321",
    "admin:123456789",
    "admin:adminadmin",
    "admin:1234567",
    "admin:7654321",
    "admin:password1234",
    "admin:admin123456",
    "admin:112233",
    "admin:102030",
    "admin:admin1",
    "admin:passw0rd",
    "admin:password01",
    "admin:admin01",
    "admin:pass123",
    "admin:abc123",
    "admin:123abc",
    "admin:admin2019",
    "admin:admin2020",
    "admin:admin2021",
    "admin:admin2022",
    "admin:admin2023",
    "admin:q1w2e3r4",
    "admin:1q2w3e4r",
    "admin:qazwsx",
    "admin:asdfgh",
    "admin:zxcvbn",
    "admin:password!",
    "admin:p@ssw0rd",
    "admin:admin!",
    "admin:1234qwer",
    "admin:qwer1234",
    "admin:1qaz2wsx",
    "admin:qwertyuiop",
    "admin:asdfghjkl",
    "admin:zxcvbnm",
    "admin:1qaz!QAZ",
    "admin:2wsx@WSX",
    "admin:3edc#EDC",
    "admin:4rfv$RFV",
    "admin:5tgb%TGB",
    "admin:6yhn^YHN",
    "admin:7ujm&UJM"
]
route_list = [
{1: '/0', 2: None},
{1: '/h264/media.smp', 2: None},
{1: '/h265/media.smp', 2: None},
{1: '/profile0/media.smp', 2: None},
{1: '/profile1/media.smp', 2: None},
{1: '/profile3/media.smp', 2: None},
{1: '/profile4/media.smp', 2: None},
{1: '/profile5/media.smp', 2: None},
{1: '/0/profile0/media.smp', 2: None},
{1: '/0/profile1/media.smp', 2: None},
{1: '/1/profile0/media.smp', 2: None},
{1: '/1/profile1/media.smp', 2: None},
{1: '/LiveChannel/0/media.smp', 2: None},
{1: '/LiveChannel/1/media.smp', 2: None},
{1: '/LiveChannel/2/media.smp', 2: None},
{1: '/LiveChannel/3/media.smp', 2: None},
{1: '/LiveChannel/3/media.smp/profile=0', 2: None},
{1: '/LiveChannel/3/media.smp/profile=1', 2: None},
{1: '/axis-media/media.amp?videocodec=h264', 2: "/axis-media/media.amp?videocodec=h264&resolution=640x480"},
{1: '/axis-media/media.amp?videocodec=h265', 2: "/axis-media/media.amp?videocodec=h265&resolution=640x480"},
# {1: '/0/1:1/main', 2: None},
# {1: '/0/usrnm:pwd/main', 2: None},
# {1: '/0/video2', 2: None},
# {1: '/0/video1', 2: None},
# {1: '/5', 2: None},
# {1: '/4', 2: None},
# {1: '/3', 2: None},
# {1: '/2', 2: None},
# {1: '/1', 2: None},
# {1: '/1.AMP', 2: None},
# {1: '/1/h264major', 2: None},
# {1: '/2/h264major', 2: None},
# {1: '/1/stream1', 2: None},
# {1: '/1080p', 2: None},
# {1: '/11', 2: None},
# {1: '/12', 2: None},
# {1: '/125', 2: None},
# {1: '/1440p', 2: None},
# {1: '/480p', 2: None},
# {1: '/4K', 2: None},
# {1: '/666', 2: None},
# {1: '/720p', 2: None},
# {1: '/AVStream1_1', 2: None},
# {1: '/CAM_ID.password.mp2', 2: None},
# {1: '/CH001.sdp', 2: None},
# {1: '/GetData.cgi', 2: None},
# {1: '/HD', 2: None},
# {1: '/HighResolutionVideo', 2: None},
# {1: '/LowResolutionVideo', 2: None},
# {1: '/MediaInput/h264', 2: None},
# {1: '/MediaInput/mpeg4', 2: None},
# {1: '/ONVIF/MediaInput', 2: None},
# {1: '/ONVIF/MediaInput?profile=4_def_profile6', 2: None},
# {1: '/StdCh1', 2: None},
# {1: '/Streaming/Channels/3', 2: None},
# {1: '/Streaming/Channels/101/', 2: '/Streaming/Channels/102/'},
# {1: '/Streaming/Channels/102', 2: None},
# {1: '/Streaming/Channels/2', 2: None},
# {1: '/Streaming/Channels/1', 2: None},
# {1: '/Streaming/Channels/301', 2: None},
# {1: '/Streaming/Channels/501', 2: None},
# {1: '/Streaming/channels/201', 2: None},
# {1: '/Streaming/channels/401', 2: None},
# {1: '/StreamingSetting?version=1.0&action=getRTSPStream&ChannelID=1&ChannelName=Channel1', 2: None},
# {1: '/VideoInput/1/h264/1', 2: None},
# {1: '/VideoInput/1/mpeg4/1', 2: None},
# {1: '/access_code', 2: None},
# {1: '/access_name_for_stream_1_to_5', 2: None},
# {1: '/api/mjpegvideo.cgi', 2: None},
# {1: '/av0_0', 2: None},
# {1: '/av2', 2: None},
# {1: '/avc', 2: None},
# {1: '/avn=2', 2: None},
# {1: '/axis-media/media.amp', 2: None},
# {1: '/axis-media/media.amp?camera=1', 2: None},
# {1: '/axis-media/media.amp?videocodec=h264', 2: None},
# {1: '/cam', 2: None},
# {1: '/cam/realmonitor', 2: None},
# {1: '/cam/realmonitor?channel=0&subtype=0', 2: None},
# {1: '/cam/realmonitor?channel=1&subtype=0', 2: None},
# {1: '/cam/realmonitor?channel=1&subtype=1', 2: None},
# {1: '/cam/realmonitor?channel=1&subtype=1&unicast=true&proto=Onvif', 2: None},
# {1: '/cam0', 2: None},
# {1: '/cam0_0', 2: None},
# {1: '/cam0_1', 2: None},
# {1: '/cam1', 2: None},
# {1: '/cam1/h264', 2: None},
# {1: '/cam1/h264/multicast', 2: None},
# {1: '/cam1/mjpeg', 2: None},
# {1: '/cam1/mpeg4', 2: None},
# {1: "/cam1/mpeg4?user='username'&pwd='password'\n", 2: None},
# {1: '/camera.stm', 2: None},
# {1: '/ch0', 2: None},
# {1: '/ch00/0', 2: None},
# {1: '/ch001.sdp', 2: None},
# {1: '/ch01.264', 2: None},
# {1: '/ch01.264?', 2: None},
# {1: '/ch01.264?ptype=tcp', 2: None},
# {1: '/ch0_0.h264', 2: None},
# {1: '/ch0_unicast_firststream', 2: None},
# {1: '/ch0_unicast_secondstream', 2: None},
# {1: '/ch1-s1', 2: None},
# {1: '/ch1/0', 2: None},
# {1: '/ch1_0', 2: None},
# {1: '/ch2/0', 2: None},
# {1: '/ch2_0', 2: None},
# {1: '/ch3/0', 2: None},
# {1: '/ch3_0', 2: None},
# {1: '/ch4/0', 2: None},
# {1: '/ch4_0', 2: None},
# {1: '/channel1', 2: None},
# {1: '/channel2', 2: None},
# {1: '/channel3', 2: None},
# {1: '/gnz_media/main', 2: None},
# {1: '/h264', 2: None},
# {1: '/h264.sdp', 2: None},
# {1: '/h264/ch1/sub/av_stream', 2: None},
# {1: '/h264/media.amp', 2: None},
# {1: '/h264Preview_01_main', 2: None},
# {1: '/h264Preview_01_sub', 2: None},
# {1: '/h264_vga.sdp', 2: None},
# {1: '/image.mpg', 2: None},
# {1: '/img/media.sav', 2: None},
# {1: '/img/media.sav?channel=1', 2: None},
# {1: '/img/video.asf', 2: None},
# {1: '/img/video.sav', 2: None},
# {1: '/ioImage/1', 2: None},
# {1: '/ipcam.sdp', 2: None},
# {1: '/ipcam_h264.sdp', 2: None},
# {1: '/ipcam_mjpeg.sdp', 2: None},
# {1: '/live', 2: None},
# {1: '/live.sdp', 2: None},
# {1: '/live/av0', 2: None},
# {1: '/live/ch0', 2: None},
# {1: '/live/ch00_0', 2: None},
# {1: '/live/ch01_0', 2: None},
# {1: '/live/h264', 2: None},
# {1: '/live/main', 2: None},
# {1: '/live/main0', 2: None},
# {1: '/live/mpeg4', 2: None},
# {1: '/live1.sdp', 2: None},
# {1: '/live3.sdp', 2: None},
# {1: '/live_mpeg4.sdp', 2: None},
# {1: '/live_st1', 2: None},
# {1: '/livestream', 2: None},
# {1: '/livestream/', 2: None},
# {1: '/main', 2: None},
# {1: '/media', 2: None},
# {1: '/media.amp', 2: None},
# {1: '/media.amp?streamprofile=Profile1', 2: None},
# {1: '/media/media.amp', 2: None},
# {1: '/media/video1', 2: None},
# {1: '/medias2', 2: None},
# {1: '/mjpeg/media.smp', 2: None},
# {1: '/mp4', 2: None},
# {1: '/mpeg/media.amp', 2: None},
# {1: '/mpeg4', 2: None},
# {1: '/mpeg4/1/media.amp', 2: None},
# {1: '/mpeg4/media.amp', 2: None},
# {1: '/mpeg4/media.smp', 2: None},
# {1: '/mpeg4unicast', 2: None},
# {1: '/mpg4/rtsp.amp', 2: None},
# {1: '/multicaststream', 2: None},
# {1: '/now.mp4', 2: None},
# {1: '/nph-h264.cgi', 2: None},
# {1: '/nphMpeg4/g726-640x', 2: None},
# {1: '/nphMpeg4/g726-640x48', 2: None},
# {1: '/nphMpeg4/g726-640x480', 2: None},
# {1: '/nphMpeg4/nil-320x240', 2: None},
# {1: '/onvif-media/media.amp', 2: None},
# {1: '/onvif1', 2: None},
# {1: '/pass@10.0.0.5:6667/blinkhd', 2: None},
# {1: '/play1.sdp', 2: None},
# {1: '/play2.sdp', 2: None},
# {1: '/profile2/media.smp', 2: None},
# {1: '/profile5/media.smp', 2: None},
# {1: '/rtpvideo1.sdp', 2: None},
# {1: '/rtsp_live0', 2: None},
# {1: '/rtsp_live1', 2: None},
# {1: '/rtsp_live2', 2: None},
# {1: '/rtsp_tunnel', 2: None},
# {1: '/rtsph264', 2: None},
# {1: '/snap.jpg', 2: None},
# {1: '/stream', 2: None},
# {1: '/stream.sdp', 2: None},
# {1: '/stream/0', 2: None},
# {1: '/stream/1', 2: None},
# {1: '/stream/live.sdp', 2: None},
# {1: '/stream1', 2: None},
# {1: '/streaming/channels/0', 2: None},
# {1: '/streaming/channels/1', 2: None},
# {1: '/streaming/channels/101', 2: None},
# {1: '/Streaming/Unicast/channels/101', 2: None},
# {1: '/tcp/av0_0', 2: None},
# {1: '/test', 2: None},
# {1: '/tmpfs/auto.jpg', 2: None},
# {1: '/trackID=1', 2: None},
# {1: '/ucast/11', 2: None},
# {1: '/udp/av0_0', 2: None},
# {1: '/udp/unicast/aiphone_H264', 2: None},
# {1: '/udpstream', 2: None},
# {1: '/user.pin.mp2', 2: None},
# {1: '/user=admin&password=&channel=1&stream=0.sdp?', 2: None},
# {1: '/user=admin&password=&channel=1&stream=0.sdp?real_stream', 2: None},
# {1: '/user=admin_password=?????_channel=1_stream=0.sdp?real_stream', 2: None},
# {1: '/user=admin_password=R5XFY888_channel=1_stream=0.sdp?real_stream', 2: None},
# {1: '/user_defined', 2: None},
# {1: '/v2', 2: None},
# {1: '/video', 2: None},
# {1: '/video.3gp', 2: None},
# {1: '/video.h264', 2: None},
# {1: '/video.mjpg', 2: None},
# {1: '/video.mp4', 2: None},
# {1: '/video.pro1', 2: None},
# {1: '/video.pro2', 2: None},
# {1: '/video.pro3', 2: None},
# {1: '/video0', 2: None},
# {1: '/video0.sdp', 2: None},
# {1: '/video1', 2: None},
# {1: '/video1+audio1', 2: None},
# {1: '/video1.sdp', 2: None},
# {1: '/videoMain', 2: None},
# {1: '/videoinput_1/h264_1/media.stm', 2: None},
# {1: '/videostream.asf', 2: None},
# {1: '/vis', 2: None},
# {1: '/wfov', 2: None},
# {1: '/PSIA/Streaming/channels/1?videoCodecType=MPEG4', 2: None},
# {1: '/cam1/onvif-h264', 2: None},
# {1: '/h264_stream', 2: None},
# {1: '/rtsph2641080p', 2: None},
# {1: '/0/av0', 2: None},
# {1: '/0/av1', 2: None},
# {1: '/0/av2', 2: None},
# {1: '/0/av3', 2: None},
# {1: '/live/0', 2: None},
# {1: '/live/1', 2: None},
# {1: '/live/2', 2: None},
# {1: '/live/3', 2: None},
# {1: '/main/av_stream', 2: None},
# {1: '/sub/av_stream', 2: None},
# {1: '/ONVIF/channels/1', 2: None},
# {1: '/ONVIF/channels/2', 2: None},
# {1: '/ONVIF/channels/3', 2: None},
# {1: '/1/h264minor', 2: None},
# {1: '/1/h264/main', 2: None},
# {1: '/1/h264/sub', 2: None},
# {1: '/2/h264/main', 2: None},
# {1: '/2/h264/sub', 2: None},
# {1: '/3/h264/main', 2: None},
# {1: '/3/h264/sub', 2: None},
# {1: '/channel2', 2: None},
# {1: '/channel3', 2: None},
# {1: '/channel4', 2: None},
# {1: '/video2', 2: None},
# {1: '/video3', 2: None},
# {1: '/video4', 2: None},
# {1: '/live/av1', 2: None},
# {1: '/live/av2', 2: None},
# {1: '/live/av3', 2: None},
# {1: '/cam/realmonitor?channel=2&subtype=0', 2: None},
# {1: '/cam/realmonitor?channel=3&subtype=0', 2: None},
# {1: '/cam/realmonitor?channel=4&subtype=0', 2: None},
# {1: '/Streaming/Channels/3', 2: None},
# {1: '/Streaming/Channels/4', 2: None},
# {1: '/Streaming/channels/202', 2: None},
# {1: '/Streaming/channels/203', 2: None},
# {1: '/Streaming/channels/204', 2: None},
# {1: '/1/h264/2', 2: None},
# {1: '/1/h264/3', 2: None},
# {1: '/1/h264/4', 2: None},
# {1: '/live/ch02_0', 2: None},
# {1: '/live/ch03_0', 2: None},
# {1: '/live/ch04_0', 2: None},
# {1: '/Streaming/Channels/1/Preview', 2: None},
# {1: '/Streaming/Channels/2/Preview', 2: None},
# {1: '/Streaming/Channels/3/Preview', 2: None},
# {1: '/Streaming/Channels/4/Preview', 2: None},
# {1: '/cam/realmonitor?channel=2&subtype=1', 2: None},
# {1: '/cam/realmonitor?channel=3&subtype=1', 2: None},
# {1: '/cam/realmonitor?channel=4&subtype=1', 2: None},
# {1: '/1/h264/preview', 2: None},
# {1: '/2/h264/preview', 2: None},
# {1: '/3/h264/preview', 2: None},
# {1: '/4/h264/preview', 2: None},
# {1: '/video2.sdp', 2: None},
# {1: '/video3.sdp', 2: None},
# {1: '/video4.sdp', 2: None},
# {1: '/ONVIF/channels/4', 2: None},
# {1: '/ONVIF/channels/5', 2: None},
# {1: '/ONVIF/channels/6', 2: None}
]

additional_list = [
# {1: '/cam/realmonitor?channel=1&subtype=0', 2: '/cam/realmonitor?channel=1&subtype=1'},
{1: '/axis-media/media.amp?videocodec=h264', 2: "/axis-media/media.amp?videocodec=h264&resolution=640x480"},
{1: '/axis-media/media.amp?videocodec=h265', 2: "/axis-media/media.amp?videocodec=h265&resolution=640x480"},
]

options = {
    "find_port_threads": 255,
    "route_threads": 255,
    "credential_threads": 255,
    "api_threads": 255,
    "onvif_threads": 255,
    "screenshot_threads":255,
    "ports": [554, 8554, 5554,''],
    "timeout": 1,
    "ip_list": [],
    "route_list": route_list,
    "credential_list": credential_list,
    "file_path": None,
    "callback_result": None,
    "skip_ip": False,
    "clear_scanned_ips": False,
    "additional_list": additional_list,
    "start_time": None,
    "end_time": None
}

class ScanState:
    Init = "Init"
    Play = "Play"
    Pause = "Pause"
    Stop = "Stop"
    End = "End"

class Config:
    __instance = None
    @staticmethod
    def get_instance():
        if Config.__instance is None:
            Config.__instance = Config()
        return Config.__instance
    
    def __init__(self):
        super().__init__()
        self.find_port_threads = 255
        self.route_threads = 255
        self.credential_threads = 255
        self.api_threads = 255
        self.onvif_threads = 255
        self.screenshot_threads = 255
        self.ports = [554,8554, 5554,'']
        self.timeout = 1
        self.ip_list = []
        self.route_list = route_list
        self.credential_list = credential_list
        self.file_path = None
        self.callback_result = None
        self.skip_ip = False
        self.clear_scanned_ips = False
        self.state = ScanState.Init
        self.additional_list = additional_list
        self.start_time = None
        self.end_time = None

    def add_options(self, options: dict):
        self.find_port_threads = options.get("find_port_threads",255)
        self.route_threads = options.get("route_threads",255)
        self.credential_threads = options.get("credential_threads",255)
        self.api_threads = options.get("api_threads",255)
        self.onvif_threads = options.get("onvif_threads",255)
        self.screenshot_threads = options.get("screenshot_threads",255)
        self.ports = options.get("port",self.ports)
        self.timeout = options.get("timeout",1)
        self.ip_list = options.get("ip_list",[])
        self.route_list = options.get("route_list",self.route_list)
        self.credential_list = options.get("credential_list",self.credential_list)
        self.file_path = options.get("file_path",self.file_path)
        self.callback_result = options.get("callback_result",self.callback_result)
        self.skip_ip = options.get("skip_ip",False)   
        self.clear_scanned_ips = options.get("clear_scanned_ips",False)
        self.start_time = options.get("start_time",None)
        self.end_time = options.get("end_time",None)        

config = Config.get_instance()
