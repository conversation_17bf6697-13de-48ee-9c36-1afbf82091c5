import socket
from enum import Enum
from ipaddress import ip_address
from time import sleep
from typing import List, Optional, Union

from cam_scan.modules.packet import describe
from cam_scan.modules.utils import find
from cam_scan.modules.cam_info import CamInfo
MAX_RETRIES = 2


class AuthMethod(Enum):
    NONE = 0
    BASIC = 1
    DIGEST = 2


class Status(Enum):
    CONNECTED = 0
    TIMEOUT = 1
    UNIDENTIFIED = 100
    NONE = -1

    @classmethod
    def from_exception(cls, exception: Exception):
        if type(exception) is type(socket.timeout()) or type(exception) is type(
            TimeoutError()
        ):
            return cls.TIMEOUT
        else:
            return cls.UNIDENTIFIED


class CamInfoClient:
    __slots__ = (
        "ip",
        "user",
        "password",
        "port",
        "brand",
        "model",
        "mac_address",
        "serial_number",
        "nvr",
        "number_cameras",
        "credentials",
        "routes",
        "status",
        "auth_method",
        "last_error",
        "realm",
        "nonce",
        "socket",
        "timeout",
        "packet",
        "cseq",
        "data",
        "state",
        "ports_opened",
        "device_type",
        "device_name",
        "cam_cfg",
        "no",
        "total",
        "records",
        "metadata",
        "error_info",
        "has_camera",
    )

    def __init__(
        self,
        ip: str,
        port: int = 554,
        timeout: int = 2,
        credentials: str = ":",
        total: Optional[int] = 0,
    ) -> None:
        try:
            ip_address(ip)
        except ValueError as e:
            raise e

        if port not in range(65536):
            raise ValueError(f"{port} is not a valid port")

        self.ip = ip
        self.user = None
        self.password = None
        self.port = port
        self.brand = None
        self.model = None
        self.mac_address = None
        self.serial_number = None
        self.nvr = None
        self.number_cameras = 1
        self.credentials = credentials
        self.routes: List[str] = []
        self.status: Status = Status.NONE
        self.auth_method: AuthMethod = AuthMethod.NONE
        self.last_error: Union[Exception, None] = None
        self.realm: str = ""
        self.nonce: str = ""
        self.socket = None
        self.timeout = timeout
        self.packet = ""
        self.cseq = 0
        self.data = ""
        self.state = False
        self.ports_opened = []
        self.device_type = ""
        self.device_name = ""
        self.cam_cfg = []
        self.no = 0
        self.total = total
        self.records = []
        self.metadata = []
        self.error_info = ''
        self.has_camera = False

    @property
    def route(self):
        if len(self.routes) > 0:
            return self.routes[0]
        else:
            return {1:""}

    @property
    def is_connected(self):
        return self.status is Status.CONNECTED

    @property
    def is_authorized(self):
        return "200" in self.data

    def connect(self, port: int = None):
        if self.is_connected:
            return True

        if port is None:
            port = self.port
        else:
            self.port = port

        self.packet = ""
        self.cseq = 0
        self.data = ""
        retry = 0
        while retry < MAX_RETRIES and not self.is_connected:
            try:
                self.socket = socket.create_connection((self.ip, port), self.timeout)
            except Exception as e:
                self.status = Status.from_exception(e)
                self.last_error = e

                retry += 1
                sleep(0.5)
            else:
                self.status = Status.CONNECTED
                self.last_error = None

                return True

        return False

    def authorize(self, port=None, route=None, credentials=None):
        if not self.is_connected:
            return False

        if port is None:
            port = self.port
        if route is None:
            route = self.route.get(1,None)
        if credentials is None:
            credentials = self.credentials

        self.cseq += 1
        self.packet = describe(
            self.ip, port, route, self.cseq, credentials, self.realm, self.nonce
        )
        try:
            self.socket.sendall(self.packet.encode())
            self.data = self.socket.recv(1024).decode()
        except Exception as e:
            self.status = Status.from_exception(e)
            self.last_error = e
            self.socket.close()
            # print(f'authorize2 {self.last_error}')
            return False

        if not self.data:
            return False

        if "Basic" in self.data:
            self.auth_method = AuthMethod.BASIC
        elif "Digest" in self.data:
            self.auth_method = AuthMethod.DIGEST
            self.realm = find("realm", self.data)
            self.nonce = find("nonce", self.data)
        else:
            self.auth_method = AuthMethod.NONE
        return True

    @staticmethod
    def get_rtsp_url(
        ip: str, port: Union[str, int] = 554, credentials: str = ":", route: str = "/"
    ):
        """Return URL in RTSP format."""
        if credentials != ":":
            ip_prefix = f"{credentials}@"
        else:
            ip_prefix = ""
        return f"rtsp://{ip_prefix}{ip}:{port}{route}"

    def __str__(self) -> str:
        return self.get_rtsp_url(self.ip, self.port, self.credentials, self.route.get(1,None))

    def __rich__(self) -> str:
        return f"[underline cyan]{self.__str__()}[/underline cyan]"

    def output(self):
        return {"ip":self.ip,
                "user":self.user,
                "password":self.password,
                "port":self.port,
                "brand":self.brand,
                "model":self.model,
                "mac_address":self.mac_address,
                "serial_number":self.serial_number,
                "nvr":self.nvr,
                "number_cameras":self.number_cameras,
                "device_type":self.device_type,
                "device_name": self.device_name,
                "cam_cfg": self.cam_cfg,
                "state": self.state,
                "no": self.no,
                "total": self.total,
                "records": self.records,
                "metadata": self.metadata,
                'error_info': self.error_info,
                'has_camera': self.has_camera
                }
