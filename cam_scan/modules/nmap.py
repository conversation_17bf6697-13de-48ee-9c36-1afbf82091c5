import re
import subprocess
import xml.etree.ElementTree as ET

def scan_port(ip_address):
    # print(f'scan_port: ip_address {ip_address}')
    # <PERSON><PERSON><PERSON> lệnh nmap và lưu kết quả v<PERSON>o biến output
    command = f"nmap -Pn --top-ports 1000 {ip_address}"
    process = subprocess.Popen(command, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    output, _ = process.communicate()

    # print(f'scan_port: output {output}')
    # Decode the byte output to string
    output = output.decode('utf-8')

    # Regular expression to extract port numbers
    ports = re.findall(r'(\d+/tcp)', output)

    for i in range(len(ports)):
        ports[i] = ports[i].replace('/tcp', '')
        
    result = {'ports': None,'mac': None,'brand': None}
    result.update({'ports': ports})
    return result

def scan_info(ip_address, input_port=81):
    print(f'scan_info: ip_address {ip_address}')
    # Run nmap command and store the output
    command = f"nmap -p{input_port} -A {ip_address}"
    print(f'scan_info: command {command}')
    process = subprocess.Popen(command, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    output, _ = process.communicate()
    output = output.decode()  # Convert bytes to string
    print(f'scan_info: output {output}')

    # Parse the output to find specific information
    # Look for "http-favicon" in the output
    favicon_info = None
    if 'http-favicon' in output:
        start_idx = output.find('http-favicon') + len('http-favicon: ')
        end_idx = output.find('\n', start_idx)
        favicon_info = output[start_idx:end_idx].strip()
    
    print(f'scan_info: favicon_info {favicon_info}')
    return favicon_info


# # # IP address bạn muốn quét
# ip_address = "*************"

# # Chạy lệnh nmap và lấy thông tin về địa chỉ MAC và vendor
# mac_vendor_info = run_nmap(ip_address)

# # In ra thông tin về địa chỉ MAC và vendor
# for mac, vendor in mac_vendor_info.items():
#     print(f"MAC Address: {mac} - Vendor: {vendor}")