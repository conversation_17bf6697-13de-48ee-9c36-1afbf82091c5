

from cam_scan.cam_scan import start_threads,wait_for
import logging
logger = logging.getLogger(__name__)
from time import sleep
from queue import Queue
import re
import platform 
import subprocess 
from pathlib import Path
import ipaddress
def scan_address_ip(rtsp):
    # Phuong phap detect ip username duoi day chi dung khi 'username' va 'password' khong chua ky tu dac biet la (':','/')
    logger.debug(f'rtsp = {rtsp}')
    # detect 1
    # format  rtsp://user:password@<DeviceIP>:port/profile
    try:
        url = re.findall(r'rtsp://(.*?):(.*?):', rtsp)[0]
        if len(url) == 2:
            username = url[0]
            pass_ip = url[1]
            list_string = pass_ip.split('@')
            if len(list_string) > 1:
                ip = list_string[-1]
                password = pass_ip.split('@' + ip)[0]
                logger.debug(f'ip = {ip}')
                logger.debug(f'password = {password}')
                return {'username': username,'ip': ip, 'password': password, 'port': 80,'detect':'success'}
            else:
                pass
        else:
            pass
    except Exception as e:
        pass
    # detect 2
    # format rtsp://user:password@<ip>/
    try:
        url = re.findall(r'rtsp://(.*?)/', rtsp)[0]
        username, pass_ip = url.split(':')
        list_string = pass_ip.split('@')
        if len(list_string) > 1:
            ip = list_string[-1]
            password = pass_ip.split('@' + ip)[0]
            return {'username': username,'ip': ip, 'password': password, 'port': 80,'detect':'success'}
        else:
            pass

    except Exception as e:
        pass
    # detect 3
    # format rtsp://user:password@<ip>
    try:
        # detect 'rtsp://' co trong link khong
        protocol,url2 = rtsp.split('rtsp://')
        username, pass_ip = url2.split(':')
        list_string = pass_ip.split('@')
        if len(list_string) > 1:
            ip = list_string[-1]
            password = pass_ip.split('@' + ip)[0]
            return {'username': username,'ip': ip, 'password': password, 'port': 80,'detect':'success'}
        else:
            pass
    except Exception as e:
        pass
    # detect 4
    # format rtsp://<ip>:<port>/
    try:
        url = re.findall(r'rtsp://(.*?)/', rtsp)[0]
        ip, port = url.split(':')
        return {'username': None,'ip': ip, 'password': None, 'port': 80,'detect':'success'}
    except Exception as e:
        pass

    # detect 5
    # format rtsp://<ip>/
    try:
        ip = re.findall(r'rtsp://(.*?)/', rtsp)[0]
        return {'username': None,'ip': ip, 'password': None, 'port': 80,'detect':'success'}
    except Exception as e:
        pass
    
    # detect 6
    # format rtsp://<ip>
    try:
        protocol, ip = rtsp.split('rtsp://')
        return {'username': None,'ip': ip, 'password': None, 'port': 80,'detect':'success'}
    except Exception as e:
        pass

    return {'username': None,'ip': None, 'password': None, 'port': 80,'detect':'error'}

def ping(host):
    """
    Returns True if host (str) responds to a ping request.
    Remember that a host may not respond to a ping (ICMP) request even if the host name is valid.
    """

    # Option for the number of packets as a function of
    param = '-n' if platform.system().lower() == 'windows' else '-c'

    # Building the command. Ex: "ping -n 1 google.com" on Windows
    command = ['ping', param, '1', host]

    # Use CREATE_NO_WINDOW flag on Windows to suppress the command window
    creation_flags = subprocess.CREATE_NO_WINDOW if platform.system().lower() == 'windows' else 0
    startupinfo = None
    if platform.system().lower() == 'windows':
        startupinfo = subprocess.STARTUPINFO()
        startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
    # Redirect stdout and stderr to subprocess.DEVNULL to suppress output
    process = subprocess.Popen(command, creationflags=creation_flags, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL, startupinfo=startupinfo)
    process.wait()

    return process.returncode == 0
def scan_ip(input_queue: Queue, output:dict) -> None:
    while True:
        target: str = input_queue.get()
        if target is None:
            break

        try:
            result = scan_address_ip(target)
            
            if result['detect'] == 'success':
                ip= result['ip']
                username = result['username']
                password = result['password']
                port = result['port']
                if username is not None and username != 'guest':
                    credential = f'{username}:{password}'
                    if credential in output:
                        list_ip = output[credential]
                        list_ip.append(ip)
                    else:
                        output[credential] = [ip]
        except Exception as e:
            print(f'error {e}')
        input_queue.task_done()
def get_ip(file_path):
    rtsps = []
    ips = {}
    rtsp_queue = Queue()
    # Mở file trong chế độ đọc
    with open(file_path, "r") as file:
        # Đọc từng dòng trong file
        count = 0
        for line in file:
            rtsps.append(line)
            # print(f'{count} {line.strip()}')  
            count += 1
    if len(rtsps)>0:
        rtsp_threads = start_threads(30,scan_ip, rtsp_queue, ips)
        for rtsp in rtsps:
            rtsp_queue.put(rtsp)
        wait_for(rtsp_queue,rtsp_threads)
        return ips
    
def get_ip_ver2(file_path = None, output_queue: Queue = None): 
    with open(file_path, "r") as file:
        # Đọc từng dòng trong file
        count = 0
        
        for line in file:
            start_ip,end_ip = line.split("-")
            start_int = int(ipaddress.IPv4Address(start_ip))
            end_int = int(ipaddress.IPv4Address(end_ip))
            for ip in range(start_int, end_int + 1):
            # ip_list = [str(ipaddress.IPv4Address(ip)) for ip in range(start_int, end_int + 1)]
                output_queue.put(str(ipaddress.IPv4Address(ip)))


def create_file(path: Path):
    path.touch()
    
