from queue import Queue
from typing import Deque, Optional
import collections
import logging
import time
from cam_scan.modules import utils, worker
from cam_scan.modules.utils import start_threads, wait_for
from cam_scan.modules.cam_info_client import CamInfoClient
from cam_scan.modules.config import config, ScanState
from cam_scan.modules.database_manager import database


class CamScan:
    def __init__(self, debug: bool = True, options: Optional[dict] = None):
        config.add_options(options or {})
        self.logger = logging.getLogger(__name__)

    def pause(self):
        self.logger.info("Pausing scan.")
        config.state = ScanState.Pause

    def play(self):
        self.logger.info("Resuming scan.")
        config.state = ScanState.Play

    def stop(self):
        self.logger.info("Stopping scan.")
        config.state = ScanState.Stop

    def scan(self):
        self.play()
        if config.file_path:
            self.scan_from_file(config.file_path, "targets")
        elif config.ip_list:
            self.scan_from_file(config.ip_list, "ips")

    def scan_from_file(self, file_path: str, mode: str):
        ip_list = self._load_ip_list(file_path, mode)
        self.logger.info(f"Starting scan with {len(ip_list)} IPs.")
        queues, threads = self._initialize_queues_and_threads()

        if config.clear_scanned_ips:
            database.delete_data()

        while ip_list:
            if config.state == ScanState.Stop:
                break
            if not queues["route_queue"].full():
                ip = ip_list.popleft()
                client = CamInfoClient(ip=ip, timeout=config.timeout, total=len(ip_list))
                if config.skip_ip:
                    if database.add_database(client.ip):
                        queues["route_queue"].put(client)
                    else:
                        queues["result_queue"].put(client)
                else:
                    queues["route_queue"].put(client)

        self._finalize_threads(queues, threads)

    def _load_ip_list(self, file_path: str, mode: str) -> Deque[str]:
        if mode == "targets":
            return collections.deque(set(utils.load_txt(file_path, "targets")))
        return collections.deque(set(utils.load_ip_list(file_path)))

    def _initialize_queues_and_threads(self):
        queue_types = ["route_queue", "credential_queue", "find_port_queue", 
                       "api_queue", "onvif_queue", "screenshot_queue", "result_queue"]

        queues = {qt: Queue(maxsize=10000) for qt in queue_types}
        threads = {
            "route_threads": start_threads(config.route_threads, worker.brute_routes, 
                                            queues["route_queue"], queues["credential_queue"], queues["result_queue"]),
            "credential_threads": start_threads(config.credential_threads, worker.brute_credentials, 
                                            queues["credential_queue"], queues["find_port_queue"], queues["result_queue"]),
            "find_port_threads": start_threads(config.find_port_threads, worker.find_port, 
                                            queues["find_port_queue"], queues["api_queue"], queues["result_queue"]),
            "api_threads": start_threads(config.api_threads, worker.brute_api, 
                                            queues["api_queue"], queues["onvif_queue"], queues["result_queue"]),
            "onvif_threads": start_threads(config.onvif_threads, worker.brute_onvif, 
                                            queues["onvif_queue"], queues["screenshot_queue"], queues["result_queue"]),
            "screenshot_threads": start_threads(config.screenshot_threads, worker.screenshot_targets, 
                                            queues["screenshot_queue"], queues["result_queue"]),
            "result_threads": start_threads(1, worker.export_result, queues["result_queue"]),
        }
        return queues, threads

    def _finalize_threads(self, queues: dict, threads: dict):
        for queue_name, thread_name in [
            ("route_queue", "route_threads"),
            ("credential_queue", "credential_threads"),
            ("find_port_queue", "find_port_threads"),
            ("api_queue", "api_threads"),
            ("onvif_queue", "onvif_threads"),
            ("screenshot_queue", "screenshot_threads"),
            ("result_queue", "result_threads"),
        ]:
            wait_for(queues[queue_name], threads[thread_name])
            self.logger.info(f"Completed processing for {queue_name}.")

    def wait_for_stop_process(self):
        while config.state != ScanState.End:
            time.sleep(0.001)
        return True
