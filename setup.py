import setuptools

from cam_scan import __version__
with open("README.md", "r") as f:
    long_description = f.read()

setuptools.setup(
    name="cam_scan",
    version=__version__,
    description="Tool for RTSP that brute-forces routes and credentials, makes screenshots!",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://gitlab.com/woolf/RTSPbrute",
    author="Woolf",
    author_email="<EMAIL>",
    classifiers=[
        "Development Status :: 5 - Production/Stable",
        "Environment :: Console",
        "Intended Audience :: Developers",
        "Intended Audience :: Information Technology",
        "License :: OSI Approved :: GNU General Public License v3 (GPLv3)",
        "Natural Language :: English",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.6",
        "Programming Language :: Python :: 3.7",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Topic :: Internet",
        "Topic :: Multimedia :: Video :: Capture",
        "Topic :: Security",
        "Topic :: Utilities",
    ],
    keywords="netstalking rtsp brute cctv",
    project_urls={
        "Source": "https://gitlab.com/woolf/RTSPbrute",
        "Tracker": "https://gitlab.com/woolf/RTSPbrute/-/issues",
    },
    packages=setuptools.find_packages(),
    install_requires=["onvif-zeep", "av", "pydantic", "keyboard", "tqdm", "bs4"],
    python_requires=">=3.6",
    package_data={"cam_scan.api.hikvision.sdk.lib": ["*.dll"],"cam_scan.api.hikvision.sdk.lib.HCNetSDKCom": ["*.dll"]},
    entry_points={"console_scripts": ["cam_scan = cam_scan.__main__:main"]},
)
