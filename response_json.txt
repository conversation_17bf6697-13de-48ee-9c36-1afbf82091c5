{"id": 10, "params": [{"id": 1, "params": {"table": {"LocalNo": 8, "LockLoginEnable": true, "LockLoginTimes": 5, "LoginFailLockTime": 1800, "MachineAddress": "MachineAddress", "MachineName": "XVR"}}, "result": true}, {"id": 2, "params": {"type": "DH-XVR1B08-I-VN"}, "result": true}, {"id": 3, "params": {"type": "HCVR"}, "result": true}, {"id": 4, "params": {"sn": "9L0CB5CPCFA32C9"}, "result": true}, {"id": 5, "params": {"table": [{"Name": "Kênh1"}, {"Name": "Kênh1"}, {"Name": "Kênh2"}, {"Name": "Kênh3"}, {"Name": "Kênh4"}, {"Name": "IPC"}, {"Name": "IPC"}, {"Name": "Kênh8"}, {"Name": "Kênh9"}, {"Name": "Kênh10"}]}, "result": true}, {"id": 6, "params": {"table": [{"ExtraFormat": [{"Audio": {"Bitrate": 64, "Channels": [0], "Compression": "G.711Mu", "Depth": 16, "Frequency": 8000, "Pack": "DHAV"}, "AudioEnable": true, "Video": {"BitRate": 512, "BitRateControl": "CBR", "Compression": "H.264", "CustomResolutionName": "VGA", "FPS": 25, "GOP": 50, "Height": 480, "Pack": "DHAV", "Priority": 0, "Profile": "Main", "Quality": 4, "QualityRange": 6, "SVCTLayer": 1, "Width": 640}, "VideoEnable": true}, {"Audio": {"Bitrate": 64, "Channels": [0], "Compression": "G.711Mu", "Depth": 16, "Frequency": 8000, "Pack": "DHAV"}, "AudioEnable": true, "Video": {"BitRate": 512, "BitRateControl": "CBR", "Compression": "H.264", "CustomResolutionName": "VGA", "FPS": 25, "GOP": 50, "Height": 480, "Pack": "DHAV", "Priority": 0, "Profile": "Main", "Quality": 4, "QualityRange": 6, "SVCTLayer": 1, "Width": 640}, "VideoEnable": true}, {"Audio": {"Bitrate": 64, "Channels": [0], "Compression": "G.711Mu", "Depth": 16, "Frequency": 8000, "Pack": "DHAV"}, "AudioEnable": true, "Video": {"BitRate": 512, "BitRateControl": "CBR", "Compression": "H.264", "CustomResolutionName": "VGA", "FPS": 25, "GOP": 50, "Height": 480, "Pack": "DHAV", "Priority": 0, "Profile": "Main", "Quality": 4, "QualityRange": 6, "SVCTLayer": 1, "Width": 640}, "VideoEnable": true}], "MainFormat": [{"Audio": {"Bitrate": 64, "Channels": [0], "Compression": "G.711Mu", "Depth": 16, "Frequency": 8000, "Mode": 0, "Pack": "DHAV"}, "AudioEnable": true, "Video": {"BitRate": 1024, "BitRateControl": "CBR", "Compression": "H.265", "CustomResolutionName": "1080P", "FPS": 25, "GOP": 50, "Height": 1080, "Pack": "DHAV", "Priority": 0, "Profile": "Baseline", "Quality": 4, "QualityRange": 6, "SVCTLayer": 1, "Width": 1920}, "VideoEnable": true}, {"Audio": {"Bitrate": 64, "Channels": [0], "Compression": "G.711Mu", "Depth": 16, "Frequency": 8000, "Mode": 0, "Pack": "DHAV"}, "AudioEnable": true, "Video": {"BitRate": 1024, "BitRateControl": "CBR", "Compression": "H.265", "CustomResolutionName": "1080P", "FPS": 25, "GOP": 50, "Height": 1080, "Pack": "DHAV", "Priority": 0, "Profile": "Baseline", "Quality": 4, "QualityRange": 6, "SVCTLayer": 1, "Width": 1920}, "VideoEnable": true}, {"Audio": {"Bitrate": 64, "Channels": [0], "Compression": "G.711Mu", "Depth": 16, "Frequency": 8000, "Mode": 0, "Pack": "DHAV"}, "AudioEnable": true, "Video": {"BitRate": 1024, "BitRateControl": "CBR", "Compression": "H.265", "CustomResolutionName": "1080P", "FPS": 25, "GOP": 50, "Height": 1080, "Pack": "DHAV", "Priority": 0, "Profile": "Baseline", "Quality": 4, "QualityRange": 6, "SVCTLayer": 1, "Width": 1920}, "VideoEnable": true}, {"Audio": {"Bitrate": 64, "Channels": [0], "Compression": "G.711Mu", "Depth": 16, "Frequency": 8000, "Mode": 0, "Pack": "DHAV"}, "AudioEnable": true, "Video": {"BitRate": 1024, "BitRateControl": "CBR", "Compression": "H.265", "CustomResolutionName": "1080P", "FPS": 25, "GOP": 50, "Height": 1080, "Pack": "DHAV", "Priority": 0, "Profile": "Baseline", "Quality": 4, "QualityRange": 6, "SVCTLayer": 1, "Width": 1920}, "VideoEnable": true}], "SnapFormat": [{"Audio": {"Bitrate": 64, "Compression": "G.711A", "Depth": 16, "Frequency": 8000, "Pack": "DHAV"}, "AudioEnable": false, "Video": {"BitRate": 5120, "BitRateControl": "VBR", "Compression": "MJPG", "CustomResolutionName": "VGA", "FPS": 1, "GOP": 2, "Height": 480, "Pack": "DHAV", "Priority": 0, "Profile": "Baseline", "Quality": 5, "QualityRange": 6, "SVCTLayer": 1, "Width": 640}, "VideoEnable": true}, {"Audio": {"Bitrate": 64, "Compression": "G.711A", "Depth": 16, "Frequency": 8000, "Pack": "DHAV"}, "AudioEnable": false, "Video": {"BitRate": 5120, "BitRateControl": "VBR", "Compression": "MJPG", "CustomResolutionName": "VGA", "FPS": 1, "GOP": 2, "Height": 480, "Pack": "DHAV", "Priority": 0, "Profile": "Baseline", "Quality": 5, "QualityRange": 6, "SVCTLayer": 1, "Width": 640}, "VideoEnable": true}, {"Audio": {"Bitrate": 64, "Compression": "G.711A", "Depth": 16, "Frequency": 8000, "Pack": "DHAV"}, "AudioEnable": false, "Video": {"BitRate": 5120, "BitRateControl": "VBR", "Compression": "MJPG", "CustomResolutionName": "VGA", "FPS": 1, "GOP": 2, "Height": 480, "Pack": "DHAV", "Priority": 0, "Profile": "Baseline", "Quality": 5, "QualityRange": 6, "SVCTLayer": 1, "Width": 640}, "VideoEnable": true}]}, {"ExtraFormat": [{"Audio": {"AudioSource": "BNC", "BitRate": 10, "Compression": "G.711A", "Depth": 16, "Frequency": 8000, "Mode": 0, "Pack": "DHAV", "PacketPeriod": 0}, "AudioEnable": true, "Video": {"BitRate": 80, "BitRateControl": "CBR", "Compression": "H.265", "FPS": 7, "GOP": 7, "Height": 288, "Pack": "DHAV", "Profile": "High", "Quality": 4, "QualityRange": 6, "SVCTLayer": 1, "Width": 352}, "VideoEnable": true}, {"Audio": {"AudioSource": "BNC", "BitRate": 10, "Compression": "G.711A", "Depth": 16, "Frequency": 8000, "Mode": 0, "Pack": "DHAV", "PacketPeriod": 0}, "AudioEnable": false, "Video": {"BitRate": 80, "BitRateControl": "CBR", "Compression": "H.265", "FPS": 7, "GOP": 7, "Height": 288, "Pack": "DHAV", "Profile": "High", "Quality": 4, "QualityRange": 6, "SVCTLayer": 1, "Width": 352}, "VideoEnable": true}, {"Audio": {"AudioSource": "BNC", "BitRate": 10, "Compression": "G.711A", "Depth": 16, "Frequency": 8000, "Mode": 0, "Pack": "DHAV", "PacketPeriod": 0}, "AudioEnable": false, "Video": {"BitRate": 80, "BitRateControl": "CBR", "Compression": "H.265", "FPS": 7, "GOP": 7, "Height": 288, "Pack": "DHAV", "Profile": "High", "Quality": 4, "QualityRange": 6, "SVCTLayer": 1, "Width": 352}, "VideoEnable": true}], "MainFormat": [{"Audio": {"AudioSource": "BNC", "BitRate": 10, "Compression": "G.711A", "Depth": 16, "Frequency": 8000, "Mode": 0, "Pack": "DHAV", "PacketPeriod": 0}, "AudioEnable": true, "Video": {"BitRate": 512, "BitRateControl": "CBR", "Compression": "H.265", "FPS": 25, "GOP": 25, "Height": 1080, "Pack": "DHAV", "Profile": "High", "Quality": 4, "QualityRange": 6, "SVCTLayer": 1, "Width": 960}, "VideoEnable": true}, {"Audio": {"AudioSource": "BNC", "BitRate": 10, "Compression": "G.711A", "Depth": 16, "Frequency": 8000, "Mode": 0, "Pack": "DHAV", "PacketPeriod": 0}, "AudioEnable": true, "Video": {"BitRate": 512, "BitRateControl": "CBR", "Compression": "H.265", "FPS": 25, "GOP": 25, "Height": 1080, "Pack": "DHAV", "Profile": "High", "Quality": 4, "QualityRange": 6, "SVCTLayer": 1, "Width": 960}, "VideoEnable": true}, {"Audio": {"AudioSource": "BNC", "BitRate": 10, "Compression": "G.711A", "Depth": 16, "Frequency": 8000, "Mode": 0, "Pack": "DHAV", "PacketPeriod": 0}, "AudioEnable": true, "Video": {"BitRate": 512, "BitRateControl": "CBR", "Compression": "H.265", "FPS": 25, "GOP": 25, "Height": 1080, "Pack": "DHAV", "Profile": "High", "Quality": 4, "QualityRange": 6, "SVCTLayer": 1, "Width": 960}, "VideoEnable": true}], "SnapFormat": [{"Video": {"BitRate": 16, "BitRateControl": "CBR", "Compression": "MJPG", "FPS": 1.0, "GOP": 1, "Height": 288, "Pack": "DHAV", "Quality": 4, "QualityRange": 6, "SVCTLayer": 1, "Width": 352}, "VideoEnable": true}, {"Video": {"BitRate": 16, "BitRateControl": "CBR", "Compression": "MJPG", "FPS": 1.0, "GOP": 1, "Height": 288, "Pack": "DHAV", "Quality": 4, "QualityRange": 6, "SVCTLayer": 1, "Width": 352}, "VideoEnable": true}, {"Video": {"BitRate": 16, "BitRateControl": "CBR", "Compression": "MJPG", "FPS": 1.0, "GOP": 1, "Height": 288, "Pack": "DHAV", "Quality": 4, "QualityRange": 6, "SVCTLayer": 1, "Width": 352}, "VideoEnable": true}]}, {"ExtraFormat": [{"Audio": {"AudioSource": "Coaxial", "BitRate": 10, "Compression": "G.711A", "Depth": 16, "Frequency": 8000, "Mode": 0, "Pack": "DHAV", "PacketPeriod": 0}, "AudioEnable": true, "Video": {"BitRate": 80, "BitRateControl": "CBR", "Compression": "H.265", "FPS": 7, "GOP": 7, "Height": 288, "Pack": "DHAV", "Profile": "High", "Quality": 4, "QualityRange": 6, "SVCTLayer": 1, "Width": 352}, "VideoEnable": true}, {"Audio": {"AudioSource": "Coaxial", "BitRate": 10, "Compression": "G.711A", "Depth": 16, "Frequency": 8000, "Mode": 0, "Pack": "DHAV", "PacketPeriod": 0}, "AudioEnable": false, "Video": {"BitRate": 80, "BitRateControl": "CBR", "Compression": "H.265", "FPS": 7, "GOP": 7, "Height": 288, "Pack": "DHAV", "Profile": "High", "Quality": 4, "QualityRange": 6, "SVCTLayer": 1, "Width": 352}, "VideoEnable": true}, {"Audio": {"AudioSource": "Coaxial", "BitRate": 10, "Compression": "G.711A", "Depth": 16, "Frequency": 8000, "Mode": 0, "Pack": "DHAV", "PacketPeriod": 0}, "AudioEnable": false, "Video": {"BitRate": 80, "BitRateControl": "CBR", "Compression": "H.265", "FPS": 7, "GOP": 7, "Height": 288, "Pack": "DHAV", "Profile": "High", "Quality": 4, "QualityRange": 6, "SVCTLayer": 1, "Width": 352}, "VideoEnable": true}], "MainFormat": [{"Audio": {"AudioSource": "Coaxial", "BitRate": 10, "Compression": "G.711A", "Depth": 16, "Frequency": 8000, "Mode": 0, "Pack": "DHAV", "PacketPeriod": 0}, "AudioEnable": true, "Video": {"BitRate": 512, "BitRateControl": "CBR", "Compression": "H.265", "FPS": 15, "GOP": 15, "Height": 1080, "Pack": "DHAV", "Profile": "High", "Quality": 4, "QualityRange": 6, "SVCTLayer": 1, "Width": 960}, "VideoEnable": true}, {"Audio": {"AudioSource": "Coaxial", "BitRate": 10, "Compression": "G.711A", "Depth": 16, "Frequency": 8000, "Mode": 0, "Pack": "DHAV", "PacketPeriod": 0}, "AudioEnable": true, "Video": {"BitRate": 512, "BitRateControl": "CBR", "Compression": "H.265", "FPS": 15, "GOP": 15, "Height": 1080, "Pack": "DHAV", "Profile": "High", "Quality": 4, "QualityRange": 6, "SVCTLayer": 1, "Width": 960}, "VideoEnable": true}, {"Audio": {"AudioSource": "Coaxial", "BitRate": 10, "Compression": "G.711A", "Depth": 16, "Frequency": 8000, "Mode": 0, "Pack": "DHAV", "PacketPeriod": 0}, "AudioEnable": true, "Video": {"BitRate": 512, "BitRateControl": "CBR", "Compression": "H.265", "FPS": 15, "GOP": 15, "Height": 1080, "Pack": "DHAV", "Profile": "High", "Quality": 4, "QualityRange": 6, "SVCTLayer": 1, "Width": 960}, "VideoEnable": true}], "SnapFormat": [{"Video": {"BitRate": 16, "BitRateControl": "CBR", "Compression": "MJPG", "FPS": 1.0, "GOP": 1, "Height": 288, "Pack": "DHAV", "Quality": 4, "QualityRange": 6, "SVCTLayer": 1, "Width": 352}, "VideoEnable": true}, {"Video": {"BitRate": 16, "BitRateControl": "CBR", "Compression": "MJPG", "FPS": 1.0, "GOP": 1, "Height": 288, "Pack": "DHAV", "Quality": 4, "QualityRange": 6, "SVCTLayer": 1, "Width": 352}, "VideoEnable": true}, {"Video": {"BitRate": 16, "BitRateControl": "CBR", "Compression": "MJPG", "FPS": 1.0, "GOP": 1, "Height": 288, "Pack": "DHAV", "Quality": 4, "QualityRange": 6, "SVCTLayer": 1, "Width": 352}, "VideoEnable": true}]}, {"ExtraFormat": [{"Audio": {"AudioSource": "Coaxial", "BitRate": 10, "Compression": "G.711A", "Depth": 16, "Frequency": 8000, "Mode": 0, "Pack": "DHAV", "PacketPeriod": 0}, "AudioEnable": true, "Video": {"BitRate": 80, "BitRateControl": "CBR", "Compression": "H.265", "FPS": 7, "GOP": 7, "Height": 288, "Pack": "DHAV", "Profile": "High", "Quality": 4, "QualityRange": 6, "SVCTLayer": 1, "Width": 352}, "VideoEnable": true}, {"Audio": {"AudioSource": "Coaxial", "BitRate": 10, "Compression": "G.711A", "Depth": 16, "Frequency": 8000, "Mode": 0, "Pack": "DHAV", "PacketPeriod": 0}, "AudioEnable": false, "Video": {"BitRate": 80, "BitRateControl": "CBR", "Compression": "H.265", "FPS": 7, "GOP": 7, "Height": 288, "Pack": "DHAV", "Profile": "High", "Quality": 4, "QualityRange": 6, "SVCTLayer": 1, "Width": 352}, "VideoEnable": true}, {"Audio": {"AudioSource": "Coaxial", "BitRate": 10, "Compression": "G.711A", "Depth": 16, "Frequency": 8000, "Mode": 0, "Pack": "DHAV", "PacketPeriod": 0}, "AudioEnable": false, "Video": {"BitRate": 80, "BitRateControl": "CBR", "Compression": "H.265", "FPS": 7, "GOP": 7, "Height": 288, "Pack": "DHAV", "Profile": "High", "Quality": 4, "QualityRange": 6, "SVCTLayer": 1, "Width": 352}, "VideoEnable": true}], "MainFormat": [{"Audio": {"AudioSource": "Coaxial", "BitRate": 10, "Compression": "G.711A", "Depth": 16, "Frequency": 8000, "Mode": 0, "Pack": "DHAV", "PacketPeriod": 0}, "AudioEnable": true, "Video": {"BitRate": 512, "BitRateControl": "CBR", "Compression": "H.265", "FPS": 15, "GOP": 15, "Height": 1080, "Pack": "DHAV", "Profile": "High", "Quality": 4, "QualityRange": 6, "SVCTLayer": 1, "Width": 960}, "VideoEnable": true}, {"Audio": {"AudioSource": "Coaxial", "BitRate": 10, "Compression": "G.711A", "Depth": 16, "Frequency": 8000, "Mode": 0, "Pack": "DHAV", "PacketPeriod": 0}, "AudioEnable": true, "Video": {"BitRate": 512, "BitRateControl": "CBR", "Compression": "H.265", "FPS": 15, "GOP": 15, "Height": 1080, "Pack": "DHAV", "Profile": "High", "Quality": 4, "QualityRange": 6, "SVCTLayer": 1, "Width": 960}, "VideoEnable": true}, {"Audio": {"AudioSource": "Coaxial", "BitRate": 10, "Compression": "G.711A", "Depth": 16, "Frequency": 8000, "Mode": 0, "Pack": "DHAV", "PacketPeriod": 0}, "AudioEnable": true, "Video": {"BitRate": 512, "BitRateControl": "CBR", "Compression": "H.265", "FPS": 15, "GOP": 15, "Height": 1080, "Pack": "DHAV", "Profile": "High", "Quality": 4, "QualityRange": 6, "SVCTLayer": 1, "Width": 960}, "VideoEnable": true}], "SnapFormat": [{"Video": {"BitRate": 16, "BitRateControl": "CBR", "Compression": "MJPG", "FPS": 1.0, "GOP": 1, "Height": 288, "Pack": "DHAV", "Quality": 4, "QualityRange": 6, "SVCTLayer": 1, "Width": 352}, "VideoEnable": true}, {"Video": {"BitRate": 16, "BitRateControl": "CBR", "Compression": "MJPG", "FPS": 1.0, "GOP": 1, "Height": 288, "Pack": "DHAV", "Quality": 4, "QualityRange": 6, "SVCTLayer": 1, "Width": 352}, "VideoEnable": true}, {"Video": {"BitRate": 16, "BitRateControl": "CBR", "Compression": "MJPG", "FPS": 1.0, "GOP": 1, "Height": 288, "Pack": "DHAV", "Quality": 4, "QualityRange": 6, "SVCTLayer": 1, "Width": 352}, "VideoEnable": true}]}, {"ExtraFormat": [{"Audio": {"AudioSource": "Coaxial", "BitRate": 10, "Compression": "G.711A", "Depth": 16, "Frequency": 8000, "Mode": 0, "Pack": "DHAV", "PacketPeriod": 0}, "AudioEnable": true, "Video": {"BitRate": 80, "BitRateControl": "CBR", "Compression": "H.265", "FPS": 7, "GOP": 7, "Height": 288, "Pack": "DHAV", "Profile": "High", "Quality": 4, "QualityRange": 6, "SVCTLayer": 1, "Width": 352}, "VideoEnable": true}, {"Audio": {"AudioSource": "Coaxial", "BitRate": 10, "Compression": "G.711A", "Depth": 16, "Frequency": 8000, "Mode": 0, "Pack": "DHAV", "PacketPeriod": 0}, "AudioEnable": false, "Video": {"BitRate": 80, "BitRateControl": "CBR", "Compression": "H.265", "FPS": 7, "GOP": 7, "Height": 288, "Pack": "DHAV", "Profile": "High", "Quality": 4, "QualityRange": 6, "SVCTLayer": 1, "Width": 352}, "VideoEnable": true}, {"Audio": {"AudioSource": "Coaxial", "BitRate": 10, "Compression": "G.711A", "Depth": 16, "Frequency": 8000, "Mode": 0, "Pack": "DHAV", "PacketPeriod": 0}, "AudioEnable": false, "Video": {"BitRate": 80, "BitRateControl": "CBR", "Compression": "H.265", "FPS": 7, "GOP": 7, "Height": 288, "Pack": "DHAV", "Profile": "High", "Quality": 4, "QualityRange": 6, "SVCTLayer": 1, "Width": 352}, "VideoEnable": true}], "MainFormat": [{"Audio": {"AudioSource": "Coaxial", "BitRate": 10, "Compression": "G.711A", "Depth": 16, "Frequency": 8000, "Mode": 0, "Pack": "DHAV", "PacketPeriod": 0}, "AudioEnable": true, "Video": {"BitRate": 512, "BitRateControl": "CBR", "Compression": "H.265", "FPS": 15, "GOP": 15, "Height": 1080, "Pack": "DHAV", "Profile": "High", "Quality": 4, "QualityRange": 6, "SVCTLayer": 1, "Width": 960}, "VideoEnable": true}, {"Audio": {"AudioSource": "Coaxial", "BitRate": 10, "Compression": "G.711A", "Depth": 16, "Frequency": 8000, "Mode": 0, "Pack": "DHAV", "PacketPeriod": 0}, "AudioEnable": true, "Video": {"BitRate": 512, "BitRateControl": "CBR", "Compression": "H.265", "FPS": 15, "GOP": 15, "Height": 1080, "Pack": "DHAV", "Profile": "High", "Quality": 4, "QualityRange": 6, "SVCTLayer": 1, "Width": 960}, "VideoEnable": true}, {"Audio": {"AudioSource": "Coaxial", "BitRate": 10, "Compression": "G.711A", "Depth": 16, "Frequency": 8000, "Mode": 0, "Pack": "DHAV", "PacketPeriod": 0}, "AudioEnable": true, "Video": {"BitRate": 512, "BitRateControl": "CBR", "Compression": "H.265", "FPS": 15, "GOP": 15, "Height": 1080, "Pack": "DHAV", "Profile": "High", "Quality": 4, "QualityRange": 6, "SVCTLayer": 1, "Width": 960}, "VideoEnable": true}], "SnapFormat": [{"Video": {"BitRate": 16, "BitRateControl": "CBR", "Compression": "MJPG", "FPS": 1.0, "GOP": 1, "Height": 288, "Pack": "DHAV", "Quality": 4, "QualityRange": 6, "SVCTLayer": 1, "Width": 352}, "VideoEnable": true}, {"Video": {"BitRate": 16, "BitRateControl": "CBR", "Compression": "MJPG", "FPS": 1.0, "GOP": 1, "Height": 288, "Pack": "DHAV", "Quality": 4, "QualityRange": 6, "SVCTLayer": 1, "Width": 352}, "VideoEnable": true}, {"Video": {"BitRate": 16, "BitRateControl": "CBR", "Compression": "MJPG", "FPS": 1.0, "GOP": 1, "Height": 288, "Pack": "DHAV", "Quality": 4, "QualityRange": 6, "SVCTLayer": 1, "Width": 352}, "VideoEnable": true}]}, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, {"MainFormat": [{"Video": {"BitRate": 1024, "Compression": "H.264", "FPS": 25, "Height": 576, "Profile": "High", "Width": 704}, "VideoEnable": false}]}]}, "result": true}, {"id": 7, "params": {"table": {"Enable": true, "Port": 554, "RTP": {"EndPort": 40000, "StartPort": 20000}}}, "result": true}, {"id": 8, "params": {"table": {"DefaultInterface": "eth0", "Domain": "dahua", "Hostname": "HCVR", "eth0": {"DefaultGateway": "***********", "DhcpEnable": false, "DnsAutoGet": false, "DnsServers": ["*******", "*******"], "IPAddress": "*************", "MTU": 1500, "PhysicalAddress": "fc:5f:49:9e:ab:92", "SubnetMask": "*************"}}}, "result": true}, {"id": 9, "params": {"states": [{"capsState": true, "channel": 0, "connectionState": "Connected"}, {"capsState": true, "channel": 1, "connectionState": "Connected"}, {"capsState": true, "channel": 2, "connectionState": "Connected"}, {"capsState": true, "channel": 3, "connectionState": "Connected"}, {"capsState": true, "channel": 4, "connectionState": "Connected"}, {"capsState": true, "channel": 5, "connectionState": "Unconnect"}, {"capsState": true, "channel": 6, "connectionState": "Unconnect"}, {"capsState": true, "channel": 7, "connectionState": "Empty"}, {"capsState": true, "channel": 8, "connectionState": "Empty"}, {"capsState": true, "channel": 9, "connectionState": "Empty"}]}, "result": true}], "result": true, "session": "f54adc5f42b754e71a46b6f5945adc55"}