from rich.progress import Progress
import time

# Tạo một đối tượng Progress
progress = Progress()

# Th<PERSON><PERSON> các task vào Progress
task1 = progress.add_task("[green]Task 1", total=2)
task2 = progress.add_task("[blue]Task 2", total=2)

# <PERSON><PERSON><PERSON> đầu hiển thị thanh tiến trình
with progress:
    while not progress.finished:
        # <PERSON><PERSON><PERSON> nhật tiến trình của Task 1
        progress.update(task1, advance=1)
        print(f'progress.tasks = {progress.console}')
        time.sleep(0.1)  # <PERSON><PERSON><PERSON> lập thời gian xử lý

        # Cậ<PERSON> nhật tiến trình của Task 2
        progress.update(task2, advance=2)
        time.sleep(0.1)  # <PERSON><PERSON><PERSON> lập thời gian xử lý